<template>
  <view class="search-box">
    <view class="search-input-wrapper" @click="onSearchBoxClick">
      <view class="search-icon">🔍</view>
      <view class="search-input">{{ placeholder }}</view>
    </view>
  </view>
</template>

<script setup lang="ts">
interface Props {
  placeholder?: string
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '搜索冥想课程...'
})

const onSearchBoxClick = () => {
  uni.navigateTo({
    url: '/pages/search/search'
  })
}
</script>

<style scoped>
.search-box {
  padding: 32rpx 24rpx;
  position: relative;
  z-index: 1;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(247, 250, 252, 0.95) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 56rpx;
  padding: 0 36rpx;
  height: 96rpx;
  cursor: pointer;
  box-shadow: 0 8rpx 32rpx rgba(127, 176, 105, 0.08), 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.search-input-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.02) 0%, rgba(168, 208, 141, 0.01) 100%);
  pointer-events: none;
  z-index: 0;
}

.search-input-wrapper:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 12rpx 40rpx rgba(127, 176, 105, 0.12), 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.search-input-wrapper:active {
  transform: translateY(0);
  box-shadow: 0 4rpx 16rpx rgba(127, 176, 105, 0.08), 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.search-icon {
  font-size: 36rpx;
  color: #7fb069;
  margin-right: 20rpx;
  position: relative;
  z-index: 1;
  filter: drop-shadow(0 1rpx 2rpx rgba(127, 176, 105, 0.2));
}

.search-input {
  flex: 1;
  font-size: 30rpx;
  color: #718096;
  font-weight: 500;
  position: relative;
  z-index: 1;
  letter-spacing: 0.3rpx;
}
</style>