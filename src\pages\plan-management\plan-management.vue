<template>
  <view class="plan-management-page">
    <NavBar title="计划管理" :show-back="true" />
    <scroll-view class="content" scroll-y="true">
      <!-- 统计信息 -->
      <view class="stats-section">
        <text class="section-title">计划统计</text>
        <view class="stats-grid">
          <view class="stat-item">
            <text class="stat-number">{{ stats.total_plans }}</text>
            <text class="stat-label">总计划数</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ stats.completed_items }}</text>
            <text class="stat-label">已完成</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ stats.completion_rate }}%</text>
            <text class="stat-label">完成率</text>
          </view>
        </view>
      </view>

      <!-- 历史计划 -->
      <view class="history-section">
        <text class="section-title">历史计划</text>
        <view v-if="historyPlans.length > 0" class="history-list">
          <view v-for="plan in historyPlans" :key="plan.id" class="history-item">
            <image class="plan-cover" :src="plan.meditation.cover_url || 'https://picsum.photos/120/120?random=default'" mode="aspectFill" />
            <view class="plan-info">
              <text class="plan-title">{{ plan.meditation.title }}</text>
              <text class="plan-desc">{{ plan.meditation.description || '' }}</text>
              <text class="plan-time">完成时间: {{ formatTime(plan.completed_at) }}</text>
            </view>
            <view class="plan-duration">
              <text class="duration-text">{{ Math.round(plan.meditation.duration / 60) }}分钟</text>
            </view>
          </view>
        </view>
        <view v-else class="empty-state">
          <text class="empty-text">暂无历史计划</text>
        </view>

        <!-- 加载更多 -->
        <view v-if="hasMore && historyPlans.length > 0" class="load-more" @click="loadMoreHistory">
          <text class="load-more-text">{{ loadingMore ? '加载中...' : '加载更多' }}</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import NavBar from '@/components/NavBar.vue'
import { PlanApi, type HistoryPlanItem, type PlanStats } from '@/api/plan'

// 统计数据
const stats = ref<PlanStats>({
  total_plans: 0,
  total_items: 0,
  completed_items: 0,
  completion_rate: '0.00'
})

// 历史计划数据
const historyPlans = ref<HistoryPlanItem[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const hasMore = ref(true)
const loadingMore = ref(false)

// 格式化时间
const formatTime = (timeStr: string) => {
  const date = new Date(timeStr)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

// 加载统计数据
const loadStats = async () => {
  try {
    // 获取最近30天的统计
    const endDate = new Date().toISOString().split('T')[0]
    const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]

    const response = await PlanApi.getPlanStats({
      start_date: startDate,
      end_date: endDate
    })

    if (response.code === 200 && response.data) {
      stats.value = response.data
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 加载历史计划
const loadHistoryPlans = async (page: number = 1, append: boolean = false) => {
  try {
    if (!append) {
      loadingMore.value = false
    } else {
      loadingMore.value = true
    }

    // 获取最近30天的历史
    const endDate = new Date().toISOString().split('T')[0]
    const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]

    const response = await PlanApi.getPlanHistory({
      pageNum: page,
      pageSize: pageSize.value,
      start_date: startDate,
      end_date: endDate
    })

    if (response.code === 200 && response.data) {
      const newPlans = response.data.items

      if (append) {
        historyPlans.value.push(...newPlans)
      } else {
        historyPlans.value = newPlans
      }

      hasMore.value = page < response.data.pages
      currentPage.value = page
    }
  } catch (error) {
    console.error('加载历史计划失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  } finally {
    loadingMore.value = false
  }
}

// 加载更多历史计划
const loadMoreHistory = () => {
  if (!loadingMore.value && hasMore.value) {
    loadHistoryPlans(currentPage.value + 1, true)
  }
}

// 页面初始化
onMounted(() => {
  loadStats()
  loadHistoryPlans()
})

</script>

<style scoped>
.plan-management-page {
  padding-top: 176rpx;
  min-height: 100vh;
  background-color: #F7FAFC;
  display: flex;
  flex-direction: column;
}

.content {
  flex: 1;
  padding: 32rpx;
}

/* 统计部分 */
.stats-section {
  margin-bottom: 32rpx;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 24rpx;
}

.stats-grid {
  display: flex;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-number {
  font-size: 48rpx;
  font-weight: 700;
  color: #7fb069;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #718096;
}

/* 历史计划部分 */
.history-section {
  margin-bottom: 32rpx;
}

.history-list {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.history-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #e2e8f0;
}

.history-item:last-child {
  border-bottom: none;
}

.plan-cover {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
}

.plan-info {
  flex: 1;
  margin-right: 16rpx;
}

.plan-title {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 8rpx;
}

.plan-desc {
  display: block;
  font-size: 24rpx;
  color: #718096;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.plan-time {
  display: block;
  font-size: 22rpx;
  color: #a0aec0;
}

.plan-duration {
  display: flex;
  align-items: center;
}

.duration-text {
  font-size: 24rpx;
  color: #7fb069;
  font-weight: 500;
}

/* 空状态 */
.empty-state {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 80rpx 32rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.empty-text {
  font-size: 28rpx;
  color: #718096;
}

/* 加载更多 */
.load-more {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  text-align: center;
  margin-top: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.load-more-text {
  font-size: 28rpx;
  color: #7fb069;
  font-weight: 500;
}
</style>
