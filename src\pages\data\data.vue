<template>
  <view class="data-page">
    <NavBar title="我的数据" :show-back="true" />
    
    <scroll-view class="content" scroll-y="true">
      <!-- 时间段选择 -->
      <view class="period-selector">
        <view 
          v-for="period in periods" 
          :key="period.value"
          class="period-item"
          :class="{ active: selectedPeriod === period.value }"
          @click="selectPeriod(period.value)"
        >
          <text class="period-text">{{ period.label }}</text>
        </view>
      </view>
      
      <!-- 数据概览 -->
      <view class="stats-overview">
        <view class="stat-card">
          <view class="stat-icon meditation-icon">🧘‍♀️</view>
          <view class="stat-content">
            <text class="stat-value">{{ totalStats.meditationTime }}</text>
            <text class="stat-label">冥想时长(分钟)</text>
          </view>
        </view>
        
        <view class="stat-card">
          <view class="stat-icon energy-icon">⚡</view>
          <view class="stat-content">
            <text class="stat-value">{{ totalStats.energy }}</text>
            <text class="stat-label">获得能量</text>
          </view>
        </view>
        
        <view class="stat-card">
          <view class="stat-icon session-icon">✅</view>
          <view class="stat-content">
            <text class="stat-value">{{ totalStats.sessions }}</text>
            <text class="stat-label">完成次数</text>
          </view>
        </view>
      </view>
      
      <!-- 详细数据列表 -->
      <view class="data-list">
        <view class="list-header">
          <text class="list-title">详细数据</text>
        </view>
        
        <view v-if="statsData.length > 0" class="data-items">
          <view 
            v-for="item in statsData" 
            :key="item.date" 
            class="data-item"
          >
            <view class="item-date">
              <text class="date-text">{{ formatDate(item.date) }}</text>
            </view>
            <view class="item-stats">
              <view class="item-stat">
                <text class="stat-icon-small">🧘‍♀️</text>
                <text class="stat-text">{{ item.meditation_time }}分钟</text>
              </view>
              <view class="item-stat">
                <text class="stat-icon-small">⚡</text>
                <text class="stat-text">{{ item.energy_gained }}</text>
              </view>
              <view class="item-stat">
                <text class="stat-icon-small">✅</text>
                <text class="stat-text">{{ item.completed_sessions }}次</text>
              </view>
            </view>
          </view>
        </view>
        
        <view v-else class="empty-data">
          <text class="empty-icon">📊</text>
          <text class="empty-title">暂无数据</text>
          <text class="empty-desc">开始你的冥想之旅，记录美好时光</text>
        </view>
      </view>
    </scroll-view>
    
    <TabBar />
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import NavBar from '@/components/NavBar.vue'
import TabBar from '@/components/TabBar.vue'
import { UserApi, type MeditationStats } from '@/api/user'
import { useAuth } from '@/composables/useAuth'

const auth = useAuth()

// 时间段选项
const periods = [
  { label: '按天', value: 'day' },
  { label: '按周', value: 'week' },
  { label: '按月', value: 'month' },
  { label: '按年', value: 'year' }
]

// 当前选择的时间段
const selectedPeriod = ref<'day' | 'week' | 'month' | 'year'>('week')

// 统计数据
const statsData = ref<Array<{
  date: string
  meditation_time: number
  energy_gained: number
  completed_sessions: number
}>>([])

const loading = ref(false)

// 计算总统计数据
const totalStats = computed(() => {
  if (!statsData.value || !Array.isArray(statsData.value) || statsData.value.length === 0) {
    return { meditationTime: 0, energy: 0, sessions: 0 }
  }

  return statsData.value.reduce((acc, item) => {
    acc.meditationTime += item.meditation_time || 0
    acc.energy += item.energy_gained || 0
    acc.sessions += item.completed_sessions || 0
    return acc
  }, { meditationTime: 0, energy: 0, sessions: 0 })
})

// 选择时间段
const selectPeriod = (period: 'day' | 'week' | 'month' | 'year') => {
  selectedPeriod.value = period
  loadStats()
}

// 加载统计数据
const loadStats = async () => {
  if (!auth.isLoggedIn) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }

  try {
    loading.value = true
    const response = await UserApi.getMeditationStats(selectedPeriod.value)

    if (response.code === 200 && response.data) {
      // 根据实际API返回结构，直接使用 response.data 数组
      if (Array.isArray(response.data)) {
        statsData.value = response.data.map(item => ({
          date: item.period_date,
          meditation_time: Math.round(item.meditation_duration / 60), // 转换为分钟
          energy_gained: item.energy_gained,
          completed_sessions: item.tasks_completed
        }))
      } else {
        statsData.value = []
      }
    } else {
      statsData.value = []
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    statsData.value = [] // 确保在错误时也有默认值
    uni.showToast({
      title: '获取数据失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 格式化日期显示
const formatDate = (dateStr: string): string => {
  const date = new Date(dateStr)
  const now = new Date()
  
  if (selectedPeriod.value === 'day') {
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const itemDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())
    const diffTime = today.getTime() - itemDate.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays === 0) return '今天'
    if (diffDays === 1) return '昨天'
    if (diffDays === 2) return '前天'
    return `${date.getMonth() + 1}月${date.getDate()}日`
  } else if (selectedPeriod.value === 'week') {
    return `第${Math.ceil(date.getDate() / 7)}周`
  } else if (selectedPeriod.value === 'month') {
    return `${date.getMonth() + 1}月`
  } else {
    return `${date.getFullYear()}年`
  }
}

// 页面加载时获取数据
onMounted(() => {
  loadStats()
})
</script>

<style scoped>
.data-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #F0F9F0 0%, #F7FAFC 40%, #FFFFFF 100%);
}

.content {
  padding-top: 176rpx;
  padding-bottom: 100rpx;
  height: calc(100vh - 276rpx);
}

.period-selector {
  display: flex;
  padding: 32rpx;
  gap: 16rpx;
}

.period-item {
  flex: 1;
  background: #FFFFFF;
  border-radius: 24rpx;
  padding: 20rpx;
  text-align: center;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.period-item.active {
  background: #7FB069;
  border-color: #7FB069;
}

.period-text {
  font-size: 26rpx;
  font-weight: 600;
  color: #2D3748;
}

.period-item.active .period-text {
  color: #FFFFFF;
}

.stats-overview {
  display: flex;
  padding: 0 32rpx 32rpx;
  gap: 16rpx;
}

.stat-card {
  flex: 1;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(127, 176, 105, 0.1);
}

.stat-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.stat-content {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #2D3748;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #718096;
}

.data-list {
  padding: 0 32rpx 32rpx;
}

.list-header {
  margin-bottom: 24rpx;
}

.list-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #2D3748;
}

.data-items {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.data-item {
  background: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.item-date {
  margin-bottom: 20rpx;
}

.date-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #2D3748;
}

.item-stats {
  display: flex;
  justify-content: space-between;
}

.item-stat {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.stat-icon-small {
  font-size: 24rpx;
}

.stat-text {
  font-size: 24rpx;
  color: #718096;
}

.empty-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 32rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2D3748;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #718096;
  line-height: 1.5;
}
</style>
