import { useUserStore } from '@/stores/user'
import { wechatLogin, getWechatUserInfo, restoreLoginState, validateToken, type UserInfo } from '@/utils/auth'

export const useAuth = () => {
  const userStore = useUserStore()

  // 获取用户openid
  const getUserOpenid = () => {
    return userStore.openid
  }

  // 微信登录流程 - 必须在用户点击事件中调用
  const login = async (): Promise<UserInfo> => {
    try {
      // 1. 先获取用户信息（必须在用户点击事件中同步调用）
      const wxUserInfo = await getWechatUserInfo()

      // 2. 执行登录流程
      const userInfo = await wechatLogin(wxUserInfo)

      // 3. 更新store状态
      userStore.setUserInfo(userInfo)
      userStore.setOpenid(userInfo.openid)

      // token已在wechatLogin中保存到本地存储，这里同步到store
      const token = uni.getStorageSync("token")
      if (token) {
        userStore.setToken(token)
      }

      return userInfo
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    }
  }

  // 退出登录
  const logout = () => {
    userStore.clearUserData()
  }

  // 检查登录状态
  const checkLoginStatus = () => {
    return userStore.isLoggedIn
  }

  // 检查是否有openid
  const checkAuthentication = () => {
    return userStore.isAuthenticated
  }

  // 初始化用户状态 - 从本地存储恢复
  const initUserState = () => {
    const loginState = restoreLoginState()
    if (loginState) {
      userStore.setUserInfo(loginState.userInfo)
      userStore.setToken(loginState.token)
      userStore.setOpenid(loginState.userInfo.openid)
      return true
    }
    return false
  }

  // 验证并刷新用户状态
  const refreshUserState = async () => {
    if (!userStore.token) {
      return false
    }

    try {
      const isValid = await validateToken()
      if (!isValid) {
        // token无效，清除用户数据
        userStore.clearUserData()
        return false
      }
      return true
    } catch (error) {
      console.error('刷新用户状态失败:', error)
      return false
    }
  }

  return {
    // 状态
    userInfo: userStore.userInfo,
    token: userStore.token,
    openid: userStore.openid,
    isLoggedIn: userStore.isLoggedIn,
    isAuthenticated: userStore.isAuthenticated,

    // 方法
    getUserOpenid,
    login,
    logout,
    checkLoginStatus,
    checkAuthentication,
    initUserState,
    refreshUserState
  }
}
