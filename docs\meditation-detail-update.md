# 冥想详情页面更新文档

## 更新概述

根据新的API响应数据结构，对 `src/pages/meditation-detail/meditation-detail.vue` 进行了全面更新。

## 主要修改内容

### 1. 封面区域更新
- **类型显示**: 在cover-subtitle前添加了类型标识，根据type和sub_type判断显示：
  - `meditation` + `course` → "冥想课程"
  - `meditation` + `single` → "冥想"
  - `sleep` → "助眠"
  - `sound` → "声音"
- **信息显示**: 更新为 "类型 · 时长 · 收藏人数"

### 2. 详情内容分类显示
- **基本信息**: 根据是否为课程显示"课程介绍"或"内容介绍"
- **标签显示**: 新增标签展示区域，解析tags_text字段并以标签形式展示
- **课程列表**: 当type为meditation且sub_type为course时，遍历显示children中的所有子课程

### 3. 收藏图标更新
- 将原来的emoji图标改为图片图标
- 使用路径：`/static/icons/like.png` 和 `/static/icons/unlike.png`

### 4. 数据结构更新
- 更新了本地数据结构以匹配API响应
- 添加了ApiMeditationData接口
- 更新了MeditationContent接口，添加了children字段

### 5. 用户体验优化
- 课程列表项支持点击跳转到子课程详情
- 开始按钮文本根据内容类型动态显示（"开始学习"/"立刻开始"）
- 添加了美观的标签样式和课程列表样式

## API响应数据结构

```typescript
interface ApiResponse {
  code: 200,
  data: {
    id: string
    type: "meditation" | "sleep" | "sound"
    sub_type?: "course" | "single"
    parent_id?: string | null
    title: string
    description?: string
    cover_url?: string
    duration: number
    tags_text?: string
    favorite_count: number
    created_at: string
    updated_at?: string | null
    parent?: any
    children?: ChildCourse[]
    is_favorited: boolean
  }
}
```

## 新增功能

1. **类型判断逻辑**: 根据type和sub_type智能显示内容类型
2. **标签解析**: 自动解析tags_text字段并展示为标签
3. **课程导航**: 支持在课程详情页面浏览和跳转到子课程
4. **智能封面**: 优先使用API返回的cover_url，否则使用默认图片
5. **动态按钮**: 根据内容类型显示不同的操作按钮文本

## 样式更新

- 添加了标签容器样式（.tags-container, .tag-item, .tag-text）
- 添加了课程列表样式（.course-list, .course-item, .course-cover等）
- 更新了收藏图标样式以支持图片显示
- 保持了原有的美观设计风格

## 兼容性

- 保持了与现有API的兼容性
- 添加了错误处理和默认数据fallback
- 支持渐进式功能增强（如果API不返回某些字段，页面仍能正常显示）
