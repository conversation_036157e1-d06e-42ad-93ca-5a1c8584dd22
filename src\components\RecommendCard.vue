<template>
  <view class="recommend-card" @click="onCardClick">
    <image class="card-cover" :src="coverImage" mode="aspectFill" />
    <view class="card-content">
      <text class="card-title">{{ title }}</text>
      <text class="card-desc">{{ description }}</text>
      <text class="card-duration">{{ duration }}分钟</text>
    </view>
    <view class="add-button" @click.stop="onAddToPlan">
      <text class="add-text">加入计划</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  id: string
  title: string
  description: string
  cover?: string
  duration: number
  type?: 'meditation' | 'sleep' | 'sound'
}

interface Emits {
  (e: 'click', id: string): void
  (e: 'add-to-plan', id: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 计算封面图片路径
const coverImage = computed(() => {
  // 优先使用传入的封面图片
  // if (props.cover) {
  //   return props.cover
  // }

  // 如果没有封面图片，使用默认图片
  const type = props.type || 'meditation' // 默认为冥想类型
  const id = parseInt(props.id) || 1
  const typeImageIndex = ((id - 1) % 3) + 1
  const paddedIndex = String(typeImageIndex).padStart(2, '0')
  return `/static/images/${type}-card-cover-01.png`
})

const onCardClick = () => {
  emit('click', props.id)
}

const onAddToPlan = () => {
  emit('add-to-plan', props.id)
}
</script>

<style scoped>
.recommend-card {
  width: 300rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(247, 250, 252, 0.95) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(127, 176, 105, 0.08), 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  margin-right: 28rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
}

.recommend-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.02) 0%, rgba(168, 208, 141, 0.01) 100%);
  pointer-events: none;
  z-index: 0;
}

.recommend-card:hover {
  transform: translateY(-6rpx) scale(1.02);
  box-shadow: 0 16rpx 48rpx rgba(127, 176, 105, 0.12), 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.card-cover {
  width: 100%;
  height: 180rpx;
  object-fit: cover;
  transition: all 0.4s ease;
  position: relative;
  z-index: 1;
}

.recommend-card:hover .card-cover {
  transform: scale(1.05);
}

.card-content {
  padding: 24rpx;
  position: relative;
  z-index: 1;
}

.card-title {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  letter-spacing: 0.5rpx;
  line-height: 1.3;
}

.card-desc {
  display: block;
  font-size: 24rpx;
  color: #718096;
  margin-bottom: 16rpx;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  opacity: 0.9;
}

.card-duration {
  font-size: 24rpx;
  color: #7fb069;
  margin-bottom: 20rpx;
  font-weight: 600;
  background: linear-gradient(135deg, #7fb069 0%, #A8D08D 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.add-button {
  width: 100%;
  height: 72rpx;
  background: linear-gradient(135deg, #7fb069 0%, #A8D08D 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0 0 24rpx 24rpx;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.add-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.recommend-card:hover .add-button::before {
  left: 100%;
}

.add-button:active {
  transform: scale(0.98);
}

.add-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
  letter-spacing: 0.5rpx;
  position: relative;
  z-index: 1;
}
</style>