<template>
  <view class="succulent-space">
    <NavBar title="多肉空间" :show-back="true" />
    
    <scroll-view class="content" scroll-y="true">
      <!-- 多肉花园 -->
      <view class="garden-section">
        <view class="section-header">
          <text class="section-title">我的多肉花园</text>
          <text class="plant-count">{{ plantList.length }}株</text>
        </view>
        
        <view v-if="plantList.length > 0" class="plants-grid">
          <view 
            v-for="plant in plantList" 
            :key="plant.id" 
            class="plant-item"
            @click="viewPlantDetail(plant)"
          >
            <view class="plant-avatar">
              <text class="plant-emoji">{{ getPlantEmoji(plant.species) }}</text>
              <view class="plant-level">Lv.{{ plant.level }}</view>
            </view>
            <text class="plant-name">{{ plant.species }}</text>
            <view class="plant-energy">
              <text class="energy-icon">⚡</text>
              <text class="energy-value">{{ plant.energy_value }}</text>
            </view>
          </view>
        </view>
        
        <view v-else class="empty-garden">
          <text class="empty-icon">🌱</text>
          <text class="empty-title">还没有多肉植物</text>
          <text class="empty-desc">完成冥想任务可以获得多肉种子哦</text>
        </view>
      </view>
      
      <!-- 种植指南 -->
      <view class="guide-section">
        <view class="section-header">
          <text class="section-title">种植指南</text>
        </view>
        
        <view class="guide-cards">
          <view class="guide-card">
            <view class="guide-icon">🧘‍♀️</view>
            <view class="guide-content">
              <text class="guide-title">完成冥想</text>
              <text class="guide-desc">每次冥想都能为多肉提供能量</text>
            </view>
          </view>
          
          <view class="guide-card">
            <view class="guide-icon">🌟</view>
            <view class="guide-content">
              <text class="guide-title">升级多肉</text>
              <text class="guide-desc">能量达到一定值可以升级多肉</text>
            </view>
          </view>
          
          <view class="guide-card">
            <view class="guide-icon">🎁</view>
            <view class="guide-content">
              <text class="guide-title">解锁新品种</text>
              <text class="guide-desc">持续冥想可以解锁更多多肉品种</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
    
    <TabBar />
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import NavBar from '@/components/NavBar.vue'
import TabBar from '@/components/TabBar.vue'
import { UserApi, type Plant } from '@/api/user'
import { useAuth } from '@/composables/useAuth'

const auth = useAuth()

// 多肉列表数据
const plantList = ref<Plant[]>([])
const loading = ref(false)

// 获取多肉植物列表
const loadPlants = async () => {
  if (!auth.isLoggedIn) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    return
  }

  try {
    loading.value = true
    const response = await UserApi.getPlants()
    
    if (response.code === 200 && response.data) {
      plantList.value = response.data
    }
  } catch (error) {
    console.error('获取多肉列表失败:', error)
    uni.showToast({
      title: '获取多肉列表失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 根据品种获取对应的emoji
const getPlantEmoji = (species: string): string => {
  const emojiMap: Record<string, string> = {
    '仙人掌': '🌵',
    '多肉': '🪴',
    '芦荟': '🌿',
    '虎皮兰': '🌱',
    '绿萝': '🍃',
    '吊兰': '🌾'
  }
  return emojiMap[species] || '🌱'
}

// 查看多肉详情
const viewPlantDetail = (plant: Plant) => {
  uni.showModal({
    title: plant.species,
    content: `等级：Lv.${plant.level}\n能量值：${plant.energy_value}\n种植时间：${new Date(plant.created_at).toLocaleDateString()}`,
    showCancel: false
  })
}

// 页面加载时获取多肉列表
onMounted(() => {
  loadPlants()
})
</script>

<style scoped>
.succulent-space {
  min-height: 100vh;
  background: linear-gradient(180deg, #F0F9F0 0%, #F7FAFC 40%, #FFFFFF 100%);
}

.content {
  padding-top: 176rpx;
  padding-bottom: 100rpx;
  height: calc(100vh - 276rpx);
}

.garden-section {
  padding: 32rpx;
  margin-bottom: 32rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #2D3748;
}

.plant-count {
  font-size: 26rpx;
  color: #7FB069;
  background: rgba(127, 176, 105, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
}

.plants-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
}

.plant-item {
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(127, 176, 105, 0.1);
  transition: all 0.3s ease;
}

.plant-item:active {
  transform: scale(0.95);
}

.plant-avatar {
  position: relative;
  margin-bottom: 16rpx;
}

.plant-emoji {
  font-size: 64rpx;
}

.plant-level {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #7FB069;
  color: #FFFFFF;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  font-weight: 600;
}

.plant-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #2D3748;
  margin-bottom: 8rpx;
}

.plant-energy {
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.energy-icon {
  font-size: 20rpx;
}

.energy-value {
  font-size: 22rpx;
  color: #F4A261;
  font-weight: 600;
}

.empty-garden {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 32rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2D3748;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #718096;
  line-height: 1.5;
}

.guide-section {
  padding: 0 32rpx 32rpx;
}

.guide-cards {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.guide-card {
  background: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.guide-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
}

.guide-content {
  flex: 1;
}

.guide-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #2D3748;
  margin-bottom: 8rpx;
}

.guide-desc {
  font-size: 24rpx;
  color: #718096;
  line-height: 1.4;
}
</style>
