<template>
  <view class="meditation-section">
    <view class="section-header">
      <text class="section-title">{{ title }}</text>
      <text class="view-all" @click="onViewAll">{{ viewAllText || '查看全部' }}</text>
    </view>
    
    <!-- 普通横向滑动布局 -->
    <scroll-view 
      v-if="!isMoreSection"
      class="cards-scroll" 
      scroll-x="true" 
      show-scrollbar="false"
    >
      <view class="cards-container">
        <MeditationCard
          v-for="item in items"
          :key="item.id"
          :id="item.id"
          :title="item.title"
          :type="type"
          :index="item.index"
          :learners="item.learners"
          @click="onCardClick"
        />
      </view>
    </scroll-view>
    
    <!-- 更多课程垂直网格布局 -->
    <view v-else class="more-section">
      <view class="grid-container">
        <MeditationCard
          v-for="item in items"
          :key="item.id"
          :id="item.id"
          :title="item.title"
          :type="type"
          :index="item.index"
          :learners="item.learners"
          :is-grid-item="true"
          @click="onCardClick"
        />
      </view>
      <view class="view-all-button" @click="onViewAll">
        <text class="button-text">查看全部</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import MeditationCard from './MeditationCard.vue'

interface MeditationItem {
  id: string
  title: string
  index: number
  learners: number
}

interface Props {
  title: string
  items: MeditationItem[]
  type?: 'meditation' | 'sleep' | 'sound'
  viewAllText?: string
  isMoreSection?: boolean
}

interface Emits {
  (e: 'view-all', title: string): void
  (e: 'card-click', id: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const onViewAll = () => {
  emit('view-all', props.title)
}

const onCardClick = (id: string) => {
  emit('card-click', id)
}
</script>

<style scoped>
.meditation-section {
  margin-bottom: 56rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 28rpx 28rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #2d3748;
  letter-spacing: 0.5rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.02);
}

.view-all {
  font-size: 26rpx;
  color: #7fb069;
  font-weight: 600;
  background: linear-gradient(135deg, #7fb069 0%, #A8D08D 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.3s ease;
  cursor: pointer;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  position: relative;
}

.view-all::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.1) 0%, rgba(168, 208, 141, 0.05) 100%);
  border-radius: 16rpx;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.view-all:hover::before {
  opacity: 1;
}

.cards-scroll {
  white-space: nowrap;
  padding: 8rpx 0;
}

/* 隐藏水平滚动条 */
.cards-scroll::-webkit-scrollbar {
  display: none;
}

.cards-scroll {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.cards-container {
  display: flex;
  padding: 0 28rpx;
  gap: 4rpx;
}

.more-section {
  padding: 0 28rpx;
}

.grid-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 28rpx;
  margin-bottom: 40rpx;
}

.view-all-button {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #7fb069 0%, #A8D08D 100%);
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(127, 176, 105, 0.3), 0 4rpx 12rpx rgba(127, 176, 105, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.view-all-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.view-all-button:hover {
  transform: translateY(-4rpx) scale(1.02);
  box-shadow: 0 12rpx 32rpx rgba(127, 176, 105, 0.4), 0 6rpx 16rpx rgba(127, 176, 105, 0.3);
}

.view-all-button:hover::before {
  left: 100%;
}

.view-all-button:active {
  transform: translateY(-2rpx) scale(0.98);
}

.button-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
  letter-spacing: 0.5rpx;
  position: relative;
  z-index: 1;
}
</style>