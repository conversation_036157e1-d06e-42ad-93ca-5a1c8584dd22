<template>
  <view
    class="meditation-card"
    :class="{ 'grid-card': isGridItem }"
    @click="onClick"
  >
    <image class="card-cover" :src="coverImage" mode="aspectFill" />
    <view class="card-content">
      <text class="card-title">{{ title }}</text>
      <text class="card-learners">{{ learners }}人正在学</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  id: string
  title: string
  type?: 'meditation' | 'sleep' | 'sound'
  index: number
  learners: number
  isGridItem?: boolean
}

interface Emits {
  (e: 'click', id: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 计算封面图片路径
const coverImage = computed(() => {
  if (props.type) {
    // 如果指定了类型，每个类型只有3张图片，需要循环使用
    const typeImageIndex = ((props.index - 1) % 3) + 1
    const paddedIndex = String(typeImageIndex).padStart(2, '0')
    return `/static/images/${props.type}-card-cover-01.png`
  } else {
    // 如果没有指定类型，根据index循环显示所有图片
    const allImages = [
      'meditation-card-cover-01.png',
      'meditation-card-cover-02.png',
      'meditation-card-cover-03.png',
      'sleep-card-cover-01.png',
      'sleep-card-cover-02.png',
      'sleep-card-cover-03.png',
      'sound-card-cover-01.png',
      'sound-card-cover-02.png',
      'sound-card-cover-03.png'
    ]
    const imageIndex = (props.index - 1) % allImages.length
    return `/static/images/${allImages[imageIndex]}`
  }
})

const onClick = () => {
  emit('click', props.id)
}
</script>

<style scoped>
.meditation-card {
  width: 240rpx;
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-right: 24rpx;
}

.card-cover {
  width: 100%;
  height: 160rpx;
  object-fit: cover;
}

.card-content {
  padding: 16rpx;
}

.card-title {
  display: block;
  font-size: 26rpx;
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-learners {
  font-size: 22rpx;
  color: #718096;
}

.grid-card {
  width: 100%;
  margin-right: 0;
}
</style>