# API迁移文档 - 从微信云开发到Koa2后端

## 概述

本文档记录了将项目从微信云开发迁移到Koa2后端API的完整过程。

## 主要变更

### 1. 移除微信云开发依赖

- 从 `App.vue` 中移除了 `wx.cloud.init()` 和云函数调用
- 从 `profile.vue` 中移除了云数据库操作
- 删除了 `UserOpenidExample.vue` 组件
- 更新了 `wx.d.ts` 类型定义，移除云开发相关类型

### 2. 新增API请求工具

#### 文件结构
```
src/
├── api/
│   └── user.ts          # 用户相关API接口
├── utils/
│   └── request.ts       # 统一请求工具
└── pages/
    └── test-login/      # 登录测试页面
        └── test-login.vue
```

#### 核心功能
- **统一请求封装** (`src/utils/request.ts`)
  - 自动添加token到请求头
  - 统一错误处理
  - 响应拦截器处理401状态码
  - 支持加载状态显示

- **用户API接口** (`src/api/user.ts`)
  - 微信小程序登录接口
  - 用户信息获取和更新
  - 完整的TypeScript类型定义

### 3. 重构用户认证系统

#### 登录流程
1. 在用户点击事件中同步调用 `wx.getUserProfile()` 获取用户信息
2. 调用 `uni.login()` 获取微信code
3. 构建登录参数并调用后端API
4. 保存token和用户信息到本地存储
5. 更新Pinia store状态

**重要提示**: `wx.getUserProfile()` 只能在用户点击事件中直接调用，不能在异步函数中调用。因此登录方法必须在用户点击事件处理函数中同步调用。

#### 关键文件修改
- `src/utils/auth.ts` - 重写登录逻辑
- `src/composables/useAuth.ts` - 更新认证组合式函数
- `src/pages/profile/profile.vue` - 使用新的登录方法

### 4. 后端接口配置

#### API基础配置
```typescript
const API_CONFIG = {
  baseURL: 'http://127.0.0.1:3000',
  timeout: 10000,
  header: {
    'Content-Type': 'application/json'
  }
}
```

#### 登录接口
- **URL**: `/api/public/user/login`
- **方法**: POST
- **参数**:
  ```typescript
  {
    openid: string
    nickname: string
    avatar_url: string
    unionid?: string
  }
  ```
- **响应**:
  ```typescript
  {
    code: 200,
    message: '登录成功',
    data: {
      token: string,
      user: {
        id: number,
        openid: string,
        nickname: string,
        avatar_url: string,
        meditation_level: number,
        streak_days: number
      }
    }
  }
  ```

## 测试说明

### 登录测试页面
访问 `/pages/test-login/test-login` 可以测试完整的登录流程：

1. 显示当前用户状态
2. 执行微信登录
3. 显示登录日志
4. 支持退出登录

### 测试数据
当前使用测试数据进行登录：
```typescript
const loginParams = {
  openid: "openid_test_123",
  nickname: "从微信获取的昵称",
  avatar_url: "从微信获取的头像",
  unionid: "unionid_test_123"
}
```

## 注意事项

### 1. 开发环境配置
- 确保后端服务运行在 `127.0.0.1:3000`
- 在微信开发者工具中关闭域名校验（开发阶段）

### 2. 生产环境部署
- 修改 `API_CONFIG.baseURL` 为生产环境地址
- 配置合法域名到微信小程序后台
- 启用HTTPS

### 3. 微信小程序API限制
- `wx.getUserProfile()` 只能在用户TAP手势触发的事件中调用
- 不能在异步函数、定时器或其他异步回调中调用
- 必须在用户点击按钮等交互事件的处理函数中直接调用

### 4. 错误处理
- 401错误会自动清除用户数据并提示重新登录
- 网络错误会显示相应的错误提示
- 所有API调用都有完整的错误处理
- 微信API调用错误会显示具体的错误信息

### 5. 类型安全
- 所有API接口都有完整的TypeScript类型定义
- 用户信息结构与后端保持一致
- 编译时类型检查确保代码质量

## 后续优化建议

1. **真实openid获取**: 实现通过微信code换取真实openid的流程
2. **token刷新**: 实现token自动刷新机制
3. **离线缓存**: 添加API响应缓存机制
4. **错误重试**: 实现网络请求失败自动重试
5. **请求取消**: 支持取消正在进行的请求

## 相关文件

- `src/utils/request.ts` - API请求工具
- `src/api/user.ts` - 用户API接口
- `src/utils/auth.ts` - 认证工具函数
- `src/composables/useAuth.ts` - 认证组合式函数
- `src/pages/test-login/test-login.vue` - 登录测试页面
- `docs/state-management.md` - 状态管理文档
