<template>
  <view class="search-page">
    <NavBar title="搜索" />

    <!-- 搜索输入框 -->
    <view class="search-input-container">
      <view class="search-input-wrapper">
        <view class="search-icon">🔍</view>
        <input class="search-input" type="text" placeholder="搜索冥想课程..." v-model="searchKeyword" @confirm="onSearch"
          focus />
        <view class="search-btn" @click="onSearch">搜索</view>
      </view>
    </view>

    <!-- 搜索结果 -->
    <view v-if="hasSearched" class="search-results">
      <!-- 分类标签 -->
      <scroll-view class="category-tabs" scroll-x="true">
        <view class="tabs">
          <view v-for="(category, index) in categories" :key="category.id" class="tab-item"
            :class="{ active: activeCategoryIndex === index }" @click="onCategoryClick(index)">
            {{ category.name }}
          </view>
        </view>
      </scroll-view>

      <!-- 结果列表 -->
      <scroll-view class="results-content" scroll-y="true">
        <view v-if="currentResults.length > 0" class="results-grid">
          <ContentCard v-for="item in currentResults" :key="item.id" :id="item.id" :title="item.title"
            :type="item.type" :index="item.index" :learners="item.learners" :category="item.category" @click="onItemClick" />
        </view>
        <view v-else class="no-results">
          <text class="no-results-text">暂无搜索结果</text>
        </view>
      </scroll-view>
    </view>

    <!-- 搜索建议 -->
    <view v-else class="search-suggestions">
      <view class="suggestions-title">热门搜索</view>
      <view class="suggestions-list">
        <view v-for="suggestion in hotSearches" :key="suggestion" class="suggestion-item"
          @click="onSuggestionClick(suggestion)">
          {{ suggestion }}
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import NavBar from '@/components/NavBar.vue'
import ContentCard from '@/components/ContentCard.vue'
import { MeditationApi, type MeditationContent, type MeditationTag } from '@/api/meditation'

// 搜索相关
const searchKeyword = ref('')
const hasSearched = ref(false)
const activeCategoryIndex = ref(0)
const loading = ref(false)

// 分类数据
const categories = ref([
  { id: 'all', name: '全部' },
  { id: 'meditation', name: '冥想' },
  { id: 'sleep', name: '睡眠' },
  { id: 'sound', name: '声音' }
])

// 热门搜索（可以从标签API获取）
const hotSearches = ref<string[]>([])

// 搜索结果数据
const searchResults = ref<{
  all: MeditationContent[]
  meditation: MeditationContent[]
  sleep: MeditationContent[]
  sound: MeditationContent[]
}>({
  all: [],
  meditation: [],
  sleep: [],
  sound: []
})

// 转换API数据为组件需要的格式
const transformSearchResult = (content: MeditationContent, index: number) => ({
  id: content.id.toString(),
  title: content.title,
  type: content.type as 'meditation' | 'sleep' | 'sound',
  index: index + 1, // 从1开始计数
  learners: content.favorite_count || 0,
  category: content.type === 'meditation' ? '冥想' : content.type === 'sleep' ? '睡眠' : '声音'
})

// 获取类型对应的中文名称
const getTypeName = (type: string): 'meditation' | 'sleep' | 'sound' | undefined => {
  const typeMap: { [key: string]: 'meditation' | 'sleep' | 'sound' } = {
    '冥想': 'meditation',
    '睡眠': 'sleep',
    '声音': 'sound'
  }
  return typeMap[type]
}

// 加载热门搜索标签
const loadHotSearches = async () => {
  try {
    const response = await MeditationApi.getTags()
    if (response.data) {
      // 取前6个标签作为热门搜索
      hotSearches.value = response.data.slice(0, 6).map(tag => tag.name)
    }
  } catch (error) {
    console.error('加载热门搜索失败:', error)
    // 使用默认热门搜索
    hotSearches.value = ['正念冥想', '深度睡眠', '压力释放', '专注力', '情绪管理', '白噪音']
  }
}

const currentResults = computed(() => {
  const currentCategory = categories.value[activeCategoryIndex.value]
  const results = searchResults.value[currentCategory.id as keyof typeof searchResults.value] || []
  return results.map((item, index) => transformSearchResult(item, index))
})

// 执行搜索
const performSearch = async (keyword: string) => {
  if (!keyword.trim()) return

  try {
    loading.value = true

    // 并行搜索不同类型的内容
    const [allRes, meditationRes, sleepRes, soundRes] = await Promise.all([
      MeditationApi.search({ keyword, pageSize: 20 }),
      MeditationApi.search({ keyword, type: 'meditation', pageSize: 20 }),
      MeditationApi.search({ keyword, type: 'sleep', pageSize: 20 }),
      MeditationApi.search({ keyword, type: 'sound', pageSize: 20 })
    ])

    // 更新搜索结果
    searchResults.value = {
      all: allRes.data?.list || [],
      meditation: meditationRes.data?.list || [],
      sleep: sleepRes.data?.list || [],
      sound: soundRes.data?.list || []
    }

    hasSearched.value = true
  } catch (error) {
    console.error('搜索失败:', error)
    uni.showToast({
      title: '搜索失败，请重试',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 事件处理
const onSearch = () => {
  if (searchKeyword.value.trim()) {
    performSearch(searchKeyword.value.trim())
  }
}

const onCategoryClick = (index: number) => {
  activeCategoryIndex.value = index
}

const onSuggestionClick = (suggestion: string) => {
  searchKeyword.value = suggestion
  onSearch()
}

const onItemClick = (id: string) => {
  uni.navigateTo({
    url: `/pages/meditation-detail/meditation-detail?id=${id}`
  })
}

// 页面加载时获取热门搜索
onMounted(() => {
  loadHotSearches()
})
</script>

<style scoped>
.search-page {
  padding-top: 176rpx;
  min-height: 100vh;
  background: linear-gradient(180deg, #F0F9F0 0%, #F7FAFC 40%, #FFFFFF 100%);
  position: relative;
}

.search-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 400rpx;
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.08) 0%, rgba(168, 208, 141, 0.05) 50%, rgba(240, 249, 240, 0.03) 100%);
  pointer-events: none;
  z-index: 0;
}

.search-input-container {
  padding: 32rpx 24rpx;
  position: relative;
  z-index: 1;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(247, 250, 252, 0.95) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 56rpx;
  padding: 0 36rpx;
  height: 96rpx;
  box-shadow: 0 8rpx 32rpx rgba(127, 176, 105, 0.08), 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
}

.search-input-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.02) 0%, rgba(168, 208, 141, 0.01) 100%);
  pointer-events: none;
  z-index: 0;
}

.search-icon {
  font-size: 36rpx;
  color: #7fb069;
  margin-right: 20rpx;
  position: relative;
  z-index: 1;
  filter: drop-shadow(0 1rpx 2rpx rgba(127, 176, 105, 0.2));
}

.search-input {
  flex: 1;
  font-size: 30rpx;
  color: #2d3748;
  background: transparent;
  border: none;
  font-weight: 500;
  position: relative;
  z-index: 1;
  letter-spacing: 0.3rpx;
}

.search-btn {
  padding: 20rpx 28rpx;
  background: linear-gradient(135deg, #7fb069 0%, #A8D08D 100%);
  color: #ffffff;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 600;
  margin-left: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(127, 176, 105, 0.3);
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
  letter-spacing: 0.5rpx;
}

.search-btn:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 16rpx rgba(127, 176, 105, 0.4);
}

.search-btn:active {
  transform: translateY(0);
}

.search-results {
  flex: 1;
  position: relative;
  z-index: 1;
}

.category-tabs {
  padding: 32rpx 0;
}

.tabs {
  display: flex;
  padding: 0 24rpx;
  white-space: nowrap;
  gap: 8rpx;
}

.tab-item {
  padding: 20rpx 36rpx;
  border-radius: 56rpx;
  font-size: 28rpx;
  color: #718096;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(247, 250, 252, 0.9) 100%);
  backdrop-filter: blur(15rpx);
  white-space: nowrap;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  font-weight: 500;
  letter-spacing: 0.3rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(255, 255, 255, 0.6);
}

.tab-item:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 20rpx rgba(127, 176, 105, 0.08);
}

.tab-item.active {
  background: linear-gradient(135deg, #7fb069 0%, #A8D08D 100%);
  color: #ffffff;
  font-weight: 600;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
  box-shadow: 0 6rpx 20rpx rgba(127, 176, 105, 0.3);
  transform: translateY(-2rpx);
}

.results-content {
  flex: 1;
  padding: 32rpx 24rpx;
  width: calc(100% - 48rpx);
}

.results-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 28rpx;
  width: 100%;
}

.results-grid :deep(.content-card) {
  width: 100%;
}

.no-results {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(247, 250, 252, 0.9) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  margin: 40rpx 0;
  box-shadow: 0 8rpx 32rpx rgba(127, 176, 105, 0.06);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.no-results::before {
  content: '🔍';
  font-size: 80rpx;
  opacity: 0.3;
  margin-bottom: 20rpx;
}

.no-results-text {
  font-size: 30rpx;
  color: #718096;
  font-weight: 500;
  letter-spacing: 0.3rpx;
}

.search-suggestions {
  padding: 40rpx 24rpx;
  position: relative;
  z-index: 1;
}

.suggestions-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 32rpx;
  letter-spacing: 0.5rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.02);
}

.suggestions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.suggestion-item {
  padding: 20rpx 32rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(247, 250, 252, 0.95) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 40rpx;
  font-size: 28rpx;
  color: #718096;
  font-weight: 500;
  letter-spacing: 0.3rpx;
  box-shadow: 0 6rpx 20rpx rgba(127, 176, 105, 0.06), 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.suggestion-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.02) 0%, rgba(168, 208, 141, 0.01) 100%);
  pointer-events: none;
  z-index: 0;
}

.suggestion-item:hover {
  transform: translateY(-4rpx) scale(1.02);
  box-shadow: 0 12rpx 32rpx rgba(127, 176, 105, 0.12), 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  color: #7fb069;
  background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(240, 249, 240, 0.98) 100%);
}

.suggestion-item:active {
  transform: translateY(-2rpx) scale(0.98);
}
</style>