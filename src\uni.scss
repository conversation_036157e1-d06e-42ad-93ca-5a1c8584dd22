/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */

/* 多肉冥想主题色配置 */

/* 主题色系 - 绿色系多肉植物风格 */
$uni-color-primary: #7FB069; // 主色调 - 多肉绿
$uni-color-primary-light: #A8D08D; // 浅绿色
$uni-color-primary-dark: #5A8A47; // 深绿色
$uni-color-secondary: #F4A261; // 辅助色 - 暖橙色
$uni-color-accent: #E76F51; // 强调色 - 珊瑚红

/* 功能色彩 */
$uni-color-success: #7FB069; // 成功色使用主色调
$uni-color-warning: #F4A261; // 警告色使用辅助色
$uni-color-error: #E76F51; // 错误色使用强调色
$uni-color-info: #2A9D8F; // 信息色 - 青绿色

/* 文字颜色层级 */
$uni-text-color-primary: #2D3748; // 主要文字 - 深灰色
$uni-text-color-secondary: #4A5568; // 次要文字
$uni-text-color-tertiary: #718096; // 三级文字
$uni-text-color-quaternary: #A0AEC0; // 四级文字/提示文字
$uni-text-color-inverse: #FFFFFF; // 反色文字
$uni-text-color-placeholder: #CBD5E0; // 占位符文字
$uni-text-color-disable: #E2E8F0; // 禁用文字

/* 背景颜色 */
$uni-bg-color: #FFFFFF; // 主背景
$uni-bg-color-page: #F7FAFC; // 页面背景
$uni-bg-color-card: #FFFFFF; // 卡片背景
$uni-bg-color-section: #EDF2F7; // 区域背景
$uni-bg-color-hover: #F1F5F9; // 悬停背景
$uni-bg-color-active: #E2E8F0; // 激活背景
$uni-bg-color-mask: rgba(45, 55, 72, 0.4); // 遮罩背景

/* 边框颜色 */
$uni-border-color-light: #F7FAFC; // 浅边框
$uni-border-color: #E2E8F0; // 默认边框
$uni-border-color-dark: #CBD5E0; // 深边框

/* 尺寸变量 */

/* 字体大小层级 */
$uni-font-size-xs: 20rpx; // 12px - 最小字体
$uni-font-size-sm: 24rpx; // 14px - 小字体
$uni-font-size-base: 28rpx; // 16px - 基础字体
$uni-font-size-md: 32rpx; // 18px - 中等字体
$uni-font-size-lg: 36rpx; // 20px - 大字体
$uni-font-size-xl: 40rpx; // 22px - 超大字体
$uni-font-size-xxl: 48rpx; // 26px - 标题字体
$uni-font-size-display: 56rpx; // 32px - 展示字体

/* 字体粗细 */
$uni-font-weight-light: 300;
$uni-font-weight-normal: 400;
$uni-font-weight-medium: 500;
$uni-font-weight-semibold: 600;
$uni-font-weight-bold: 700;

/* 行高 */
$uni-line-height-tight: 1.2;
$uni-line-height-normal: 1.4;
$uni-line-height-relaxed: 1.6;

/* 图片尺寸 */
$uni-img-size-xs: 32rpx; // 16px
$uni-img-size-sm: 48rpx; // 24px
$uni-img-size-base: 64rpx; // 32px
$uni-img-size-lg: 96rpx; // 48px
$uni-img-size-xl: 128rpx; // 64px

/* 圆角尺寸 - 多圆角设计 */
$uni-border-radius-xs: 8rpx; // 4px
$uni-border-radius-sm: 12rpx; // 6px
$uni-border-radius-base: 16rpx; // 8px
$uni-border-radius-md: 20rpx; // 10px
$uni-border-radius-lg: 24rpx; // 12px
$uni-border-radius-xl: 32rpx; // 16px
$uni-border-radius-xxl: 48rpx; // 24px
$uni-border-radius-circle: 50%;

/* 间距系统 */
$uni-spacing-xs: 8rpx; // 4px
$uni-spacing-sm: 16rpx; // 8px
$uni-spacing-base: 24rpx; // 12px
$uni-spacing-md: 32rpx; // 16px
$uni-spacing-lg: 48rpx; // 24px
$uni-spacing-xl: 64rpx; // 32px
$uni-spacing-xxl: 96rpx; // 48px

/* 卡片间距 */
$uni-card-padding: 32rpx; // 卡片内边距
$uni-card-margin: 24rpx; // 卡片外边距
$uni-section-padding: 48rpx; // 区域内边距

/* 透明度 */
$uni-opacity-disabled: 0.3; // 组件禁用态的透明度

/* 文章场景相关 */
$uni-color-title: #2c405a; // 文章标题颜色
$uni-font-size-title: 20px;
$uni-color-subtitle: #555; // 二级标题颜色
$uni-font-size-subtitle: 18px;
$uni-color-paragraph: #3f536e; // 文章段落颜色
$uni-font-size-paragraph: 15px;
/* 
多肉冥想专用样式变量 */

/* 阴影效果 */
$uni-shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
$uni-shadow-base: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
$uni-shadow-lg: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);

/* 渐变色 */
$uni-gradient-primary: linear-gradient(135deg, #7FB069 0%, #A8D08D 100%);
$uni-gradient-secondary: linear-gradient(135deg, #F4A261 0%, #E76F51 100%);
$uni-gradient-card: linear-gradient(180deg, #FFFFFF 0%, #F7FAFC 100%);

/* 特殊效果 */
$uni-backdrop-blur: blur(20rpx);
$uni-transition-base: all 0.3s ease;
$uni-transition-fast: all 0.15s ease;

/* 多肉植物主题色板 */
$succulent-green-50: #F0F9F0;
$succulent-green-100: #C6F6D5;
$succulent-green-200: #9AE6B4;
$succulent-green-300: #68D391;
$succulent-green-400: #48BB78;
$succulent-green-500: #38A169; // 主色调
$succulent-green-600: #2F855A;
$succulent-green-700: #276749;
$succulent-green-800: #22543D;
$succulent-green-900: #1A202C;

/* 全局滚动条隐藏样式 */
/* 隐藏所有水平滚动条 */
scroll-view[scroll-x="true"]::-webkit-scrollbar {
  display: none;
}

scroll-view[scroll-x="true"] {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* 通用滚动条隐藏类 */
.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}