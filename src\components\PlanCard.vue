<template>
  <view class="plan-card" :class="{ 'completed': isCompleted }" @click="onClick">
    <view class="cover-container">
      <image class="card-cover" :src="cover" mode="aspectFill" />
    </view>
    <view class="card-content">
      <text class="course-name">{{ courseName }}</text>
      <text class="lesson-duration">{{ lessonName }} · {{ duration }}分钟</text>
      <view class="progress-section">
        <view class="learners-info">
          <text class="learners-count">已完成1/10节课</text>
          <text class="learners-count">{{ learnersCount }}人在学</text>
        </view>
        <view class="progress-bar-container">
          <view class="progress-bar">
            <view class="progress-fill" :style="{ width: progress + '%' }"></view>
          </view>
        </view>
      </view>
    </view>
    <view v-if="isCompleted" class="completed-badge">
      <text class="completed-text">✓</text>
    </view>
    <view class="action-menu" @click.stop="onMenuClick">
      <text class="menu-icon">⋯</text>
    </view>
  </view>
</template>

<script setup lang="ts">
interface Props {
  id: string
  courseName: string
  lessonName: string
  cover: string
  duration: number
  progress: number
  learnersCount: number
  isCompleted?: boolean
}

interface Emits {
  (e: 'click', id: string): void
  (e: 'delete', id: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const onClick = () => {
  emit('click', props.id)
}

const onMenuClick = () => {
  uni.showActionSheet({
    itemList: ['从计划中删除'],
    success: (res) => {
      if (res.tapIndex === 0) {
        uni.showModal({
          title: '确认删除',
          content: '确定要从计划中删除这个项目吗？',
          success: (modalRes) => {
            if (modalRes.confirm) {
              emit('delete', props.id)
            }
          }
        })
      }
    }
  })
}
</script>

<style scoped>
.plan-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(247, 250, 252, 0.95) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(127, 176, 105, 0.08), 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  margin-bottom: 32rpx;
  display: flex;
  position: relative;
  padding: 28rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.plan-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.02) 0%, rgba(168, 208, 141, 0.01) 100%);
  pointer-events: none;
  z-index: 0;
}

.plan-card:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 16rpx 48rpx rgba(127, 176, 105, 0.12), 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.plan-card.completed {
  opacity: 0.8;
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.08) 0%, rgba(168, 208, 141, 0.05) 100%);
}

.plan-card.completed::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.1) 0%, rgba(168, 208, 141, 0.05) 100%);
  pointer-events: none;
  z-index: 1;
}

.cover-container {
  margin-right: 24rpx;
  position: relative;
  z-index: 2;
}

.card-cover {
  width: 128rpx;
  height: 128rpx;
  object-fit: cover;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.plan-card:hover .card-cover {
  transform: scale(1.02);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
}

.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 128rpx;
  position: relative;
  z-index: 2;
  padding: 4rpx 0;
}

.course-name {
  font-size: 32rpx;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 12rpx;
  line-height: 1.3;
  letter-spacing: 0.5rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.02);
}

.lesson-duration {
  font-size: 26rpx;
  color: #4a5568;
  margin-bottom: 16rpx;
  font-weight: 500;
  opacity: 0.9;
  letter-spacing: 0.3rpx;
}

.progress-section {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  margin-top: auto;
}

.learners-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.learners-count {
  font-size: 22rpx;
  color: #718096;
  font-weight: 500;
  opacity: 0.8;
}

.progress-bar-container {
  display: flex;
  align-items: center;
}

.progress-bar {
  flex: 1;
  height: 8rpx;
  background: linear-gradient(90deg, #E2E8F0 0%, #F1F5F9 100%);
  border-radius: 6rpx;
  overflow: hidden;
  box-shadow: inset 0 1rpx 2rpx rgba(0, 0, 0, 0.06);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #7fb069 0%, #A8D08D 50%, #7fb069 100%);
  border-radius: 6rpx;
  transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  box-shadow: 0 1rpx 4rpx rgba(127, 176, 105, 0.3);
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.completed-badge {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 44rpx;
  height: 44rpx;
  background: linear-gradient(135deg, #7fb069 0%, #A8D08D 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(127, 176, 105, 0.4), 0 2rpx 6rpx rgba(127, 176, 105, 0.2);
  z-index: 3;
  animation: completedPulse 2s infinite;
}

.completed-text {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: bold;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.action-menu {
  position: absolute;
  top: 20rpx;
  right: 72rpx;
  width: 40rpx;
  height: 40rpx;
  background: linear-gradient(135deg, rgba(45, 55, 72, 0.15) 0%, rgba(113, 128, 150, 0.1) 100%);
  backdrop-filter: blur(10rpx);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  z-index: 3;
  transition: all 0.3s ease;
}

.action-menu:hover {
  background: linear-gradient(135deg, rgba(45, 55, 72, 0.2) 0%, rgba(113, 128, 150, 0.15) 100%);
  transform: scale(1.1);
}

.menu-icon {
  font-size: 24rpx;
  color: #4a5568;
  font-weight: bold;
  transform: rotate(90deg);
  transition: all 0.3s ease;
}

.action-menu:hover .menu-icon {
  color: #2d3748;
}

@keyframes completedPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 4rpx 12rpx rgba(127, 176, 105, 0.4), 0 2rpx 6rpx rgba(127, 176, 105, 0.2);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 6rpx 16rpx rgba(127, 176, 105, 0.5), 0 3rpx 8rpx rgba(127, 176, 105, 0.3);
  }
}
</style>