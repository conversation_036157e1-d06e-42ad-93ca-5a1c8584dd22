<template>
    <view class="tabbar">
        <view class="tabbar-item" v-for="(item, index) in tabList" :key="index"
            @click="switchTab(item.pagePath, index)">
            <view class="tabbar-icon">
                <image :src="activeIndex === index ? item.selectedIconPath : item.iconPath" class="icon" />
            </view>
            <text class="tabbar-text" :class="{ active: activeIndex === index }">
                {{ item.text }}
            </text>
        </view>
    </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

interface TabItem {
    pagePath: string
    text: string
    iconPath: string
    selectedIconPath: string
}

const currentIndex = ref(0)

const tabList: TabItem[] = [
    {
        pagePath: '/pages/plan/plan',
        text: '计划',
        iconPath: '/static/icons/plan.png',
        selectedIconPath: '/static/icons/plan-active.png'
    },
    {
        pagePath: '/pages/explore/explore',
        text: '探索',
        iconPath: '/static/icons/explore.png',
        selectedIconPath: '/static/icons/explore-active.png'
    },
    {
        pagePath: '/pages/profile/profile',
        text: '我的',
        iconPath: '/static/icons/profile.png',
        selectedIconPath: '/static/icons/profile-active.png'
    }
]

const switchTab = (pagePath: string, index: number) => {
    if (currentIndex.value === index) return

    currentIndex.value = index
    uni.switchTab({
        url: pagePath
    })
}

// 实时计算当前应该选中的tab
const getCurrentTabIndex = () => {
    const pages = getCurrentPages()
    if (pages.length === 0) return 0

    const currentPage = pages[pages.length - 1]
    const currentRoute = '/' + currentPage.route

    const activeIndex = tabList.findIndex(item => item.pagePath === currentRoute)
    return activeIndex !== -1 ? activeIndex : 0
}

// 使用计算属性实时更新当前选中的tab
const activeIndex = computed(() => {
    return getCurrentTabIndex()
})

onMounted(() => {
    // 初始化时设置正确的tab
    currentIndex.value = getCurrentTabIndex()

    // 定时检查当前页面，确保tab状态正确
    setInterval(() => {
        const correctIndex = getCurrentTabIndex()
        if (currentIndex.value !== correctIndex) {
            currentIndex.value = correctIndex
        }
    }, 500)
})
</script>

<style scoped>
.tabbar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100rpx;
    background-color: #fff;
    border-top: 1rpx solid #e5e5e5;
    display: flex;
    z-index: 999;
    padding-bottom: env(safe-area-inset-bottom);
}

.tabbar-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 10rpx 0;
}

.tabbar-icon {
    width: 48rpx;
    height: 48rpx;
    margin-bottom: 4rpx;
}

.icon {
    width: 100%;
    height: 100%;
}

.tabbar-text {
    font-size: 20rpx;
    color: #718096;
    line-height: 1.2;
}

.tabbar-text.active {
    color: #7FB069;
    font-weight: 600;
}
</style>