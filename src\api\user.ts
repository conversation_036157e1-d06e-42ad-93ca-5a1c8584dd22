// 用户相关API接口
import { api, type ApiResponse, buildQueryString } from '@/utils/request'

// 用户信息接口
export interface UserInfo {
  id: number
  openid: string
  unionid?: string
  nickname: string
  avatar_url: string
  meditation_level: number
  streak_days: number
}

// 用户基础信息接口
export interface UserProfile {
  meditation_level: number
  streak_days: number
  plant_count: number
}

// 收藏项目接口
export interface FavoriteItem {
  id: string
  user_id: string
  meditation_id: string
  created_at: string
  meditation: {
    id: string
    type: string
    sub_type: string
    parent_id: string
    title: string
    description: string
    cover_url: string
    duration: number
    tags_text: string
    audio_url?: string
    video_url?: string
    favorite_count: number
    status: string
    is_recommended: boolean
    sort_order: number
    created_at: string
    updated_at: string
  }
}

// 收藏列表响应接口
export interface FavoritesResponse {
  total: number
  pageNum: number
  pageSize: number
  pages: number
  items: FavoriteItem[]
}

// 多肉植物接口
export interface Plant {
  id: string
  user_id: string
  species: string
  energy_value: number
  level: number
  created_at: string
  updated_at?: string
}

// 冥想统计数据项接口
export interface MeditationStatsItem {
  id: string
  user_id: string
  period_type: 'day' | 'week' | 'month' | 'year'
  period_date: string
  meditation_duration: number // 以秒为单位
  energy_gained: number
  tasks_completed: number
  created_at: string
}

// 冥想统计接口 - 直接返回数组
export type MeditationStats = MeditationStatsItem[]

// 登录请求参数
export interface LoginParams {
  openid: string
  unionid?: string
  nickname: string
  avatar_url: string
}

// 登录响应数据
export interface LoginResponse {
  token: string
  user: UserInfo
}

// 微信登录code换取openid的参数
export interface WxCodeParams {
  code: string
}

// 微信登录code换取openid的响应
export interface WxCodeResponse {
  openid: string
  session_key: string
  unionid?: string
}

// 用户API类
export class UserApi {
  // 微信小程序登录
  static async login(params: LoginParams): Promise<ApiResponse<LoginResponse>> {
    return api.post<LoginResponse>('/api/public/user/login', params, {
      needAuth: false // 登录接口不需要token
    })
  }

  // 微信code换取openid (如果后端提供此接口)
  static async getOpenidByCode(params: WxCodeParams): Promise<ApiResponse<WxCodeResponse>> {
    return api.post<WxCodeResponse>('/api/public/user/code2session', params, {
      needAuth: false
    })
  }

  // 获取用户信息
  static async getUserInfo(): Promise<ApiResponse<UserInfo>> {
    return api.get<UserInfo>('/api/user/info')
  }

  // 获取用户基础信息
  static async getUserProfile(): Promise<ApiResponse<UserProfile>> {
    return api.get<UserProfile>('/api/user/profile')
  }

  // 获取用户收藏列表
  static async getFavorites(pageNum: number = 1, pageSize: number = 10): Promise<ApiResponse<FavoritesResponse>> {
    const queryString = buildQueryString({ pageNum, pageSize })
    return api.get<FavoritesResponse>(`/api/user/favorites${queryString ? `?${queryString}` : ''}`)
  }

  // 获取用户多肉列表
  static async getPlants(): Promise<ApiResponse<Plant[]>> {
    return api.get<Plant[]>('/api/user/plants')
  }

  // 获取用户冥想统计
  static async getMeditationStats(periodType: 'day' | 'week' | 'month' | 'year'): Promise<ApiResponse<MeditationStats>> {
    return api.get<MeditationStats>(`/api/user/meditation-stats?period_type=${periodType}`)
  }

  // 更新用户信息
  static async updateUserInfo(params: Partial<UserInfo>): Promise<ApiResponse<UserInfo>> {
    return api.put<UserInfo>('/api/user/info', params)
  }

  // 退出登录
  static async logout(): Promise<ApiResponse<null>> {
    return api.post<null>('/api/user/logout')
  }
}
