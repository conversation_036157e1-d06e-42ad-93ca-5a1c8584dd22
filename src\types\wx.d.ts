// 微信小程序类型声明

declare namespace WechatMiniprogram {
  // 微信登录相关
  interface LoginResult {
    code: string;
    errMsg: string;
  }

  interface LoginOptions {
    provider: string;
    success?: (res: LoginResult) => void;
    fail?: (err: any) => void;
    complete?: (res: any) => void;
  }

  // 用户信息相关
  interface UserInfo {
    nickName: string;
    avatarUrl: string;
    gender: number;
    city: string;
    province: string;
    country: string;
    language: string;
  }

  interface GetUserProfileResult {
    userInfo: UserInfo;
    rawData: string;
    signature: string;
    encryptedData: string;
    iv: string;
    errMsg: string;
  }

  interface GetUserProfileOptions {
    desc: string;
    success?: (res: GetUserProfileResult) => void;
    fail?: (err: any) => void;
    complete?: (res: any) => void;
  }
}

declare const wx: {
  login: (options: WechatMiniprogram.LoginOptions) => void;
  getUserProfile: (options: WechatMiniprogram.GetUserProfileOptions) => void;
  [key: string]: any;
};
