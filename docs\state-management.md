# Vue3 状态管理 - 替代globalData

## 概述

本项目已从Vue2的`globalData`方式升级为Vue3的现代化状态管理方案，使用Pinia作为状态管理库。

## 主要变更

### 之前 (Vue2 globalData)
```javascript
// App.vue
wx.cloud.callFunction({
  name: 'get_openId',
  success: res => {
    this.globalData.user_openid = res.result.openid
  }
})

// 其他页面使用
const app = getApp()
console.log(app.globalData.user_openid)
```

### 现在 (Vue3 Pinia)
```javascript
// App.vue
import { useUserStore } from "@/stores/user"

const userStore = useUserStore()
wx.cloud.callFunction({
  name: 'get_openId',
  success: res => {
    userStore.setOpenid(res.result.openid)
  }
})

// 其他页面使用
import { useAuth } from '@/composables/useAuth'
const auth = useAuth()
console.log(auth.openid)
```

## 新的架构

### 1. Pinia Store (`src/stores/user.ts`)
- 集中管理用户状态
- 响应式数据
- 自动同步到本地存储
- TypeScript支持

### 2. Composable (`src/composables/useAuth.ts`)
- 封装认证逻辑
- 提供便捷的API
- 可在任何组件中使用

### 3. 类型定义 (`src/types/wx.d.ts`)
- 微信小程序API类型声明
- 更好的TypeScript支持

## 使用方法

### 在组件中获取用户状态

```vue
<script setup lang="ts">
import { useAuth } from '@/composables/useAuth'

const auth = useAuth()

// 响应式状态
console.log(auth.openid.value)        // 用户openid
console.log(auth.userInfo.value)      // 用户信息
console.log(auth.isLoggedIn.value)    // 登录状态
console.log(auth.isAuthenticated.value) // 认证状态

// 方法
await auth.login()    // 登录
auth.logout()         // 退出登录
</script>
```

### 在模板中使用

```vue
<template>
  <view v-if="auth.isLoggedIn">
    <text>欢迎，{{ auth.userInfo?.nickname }}</text>
    <text>OpenID: {{ auth.openid }}</text>
  </view>
  <button v-else @click="auth.login()">登录</button>
</template>
```

## 状态持久化

所有用户状态都会自动同步到本地存储：
- `userInfo` → `uni.setStorageSync('userInfo', ...)`
- `token` → `uni.setStorageSync('token', ...)`
- `openid` → `uni.setStorageSync('user_openid', ...)`

应用启动时会自动从本地存储恢复状态。

## 优势

1. **响应式**: 状态变化自动更新UI
2. **类型安全**: 完整的TypeScript支持
3. **开发体验**: 更好的IDE支持和调试
4. **可维护性**: 集中管理，易于维护
5. **可测试性**: 更容易进行单元测试
6. **性能**: 按需更新，避免不必要的渲染

## 迁移指南

如果你有其他页面还在使用旧的方式，可以按以下步骤迁移：

1. 移除对`getApp()`的调用
2. 导入`useAuth` composable
3. 使用响应式状态替代直接访问globalData
4. 更新事件处理函数使用新的API

## 示例组件

参考 `src/components/UserOpenidExample.vue` 查看完整的使用示例。
