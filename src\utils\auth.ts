// 用户认证相关工具函数
import { UserApi, type LoginParams } from '@/api/user'

export interface UserInfo {
  id: number;
  openid: string;
  unionid?: string;
  nickname: string;
  avatar_url: string;
  meditation_level: number;
  streak_days: number;
}

// 获取微信用户信息
export const getWechatUserInfo = (): Promise<WechatMiniprogram.UserInfo> => {
  return new Promise((resolve, reject) => {
    // #ifdef MP-WEIXIN
    wx.getUserProfile({
      desc: "用于完善用户资料",
      success: (res) => {
        resolve(res.userInfo);
      },
      fail: (err) => {
        console.error("获取用户信息失败:", err);
        reject(err);
      },
    });
    // #endif

    // #ifndef MP-WEIXIN
    // 非微信小程序环境，返回模拟数据
    const mockUserInfo: WechatMiniprogram.UserInfo = {
      nickName: "多肉爱好者",
      avatarUrl: "/static/icons/avatar.png",
      gender: 1,
      city: "深圳",
      province: "广东",
      country: "中国",
      language: "zh_CN",
    };
    resolve(mockUserInfo);
    // #endif
  });
};

// 微信登录 - 获取code
export const getWechatCode = (): Promise<string> => {
  return new Promise((resolve, reject) => {
    // #ifdef MP-WEIXIN
    uni.login({
      provider: "weixin",
      success: (res) => {
        if (res.code) {
          console.log("获取code成功:", res.code);
          resolve(res.code);
        } else {
          reject(new Error("获取code失败"));
        }
      },
      fail: (err) => {
        console.error("微信登录失败:", err);
        reject(err);
      },
    });
    // #endif

    // #ifndef MP-WEIXIN
    // 非微信小程序环境，返回模拟code
    const mockCode = "mock_code_" + Date.now();
    resolve(mockCode);
    // #endif
  });
};

// 完整的登录流程 - 需要在用户点击事件中调用
export const wechatLogin = async (wxUserInfo: WechatMiniprogram.UserInfo): Promise<UserInfo> => {
  try {
    // 1. 获取微信code
    const code = await getWechatCode();
    console.log("获取到微信code:", code);

    // 2. 通过code换取openid
    const codeResponse = await UserApi.getOpenidByCode({ code });

    if (codeResponse.code !== 200 || !codeResponse.data) {
      throw new Error(codeResponse.message || "获取openid失败");
    }

    const { openid, unionid } = codeResponse.data;
    console.log("获取到openid:", openid);

    // 3. 构建登录参数
    const loginParams: LoginParams = {
      openid,
      nickname: wxUserInfo.nickName,
      avatar_url: wxUserInfo.avatarUrl,
      unionid
    };

    console.log("登录参数:", loginParams);

    // 4. 调用后端登录接口
    const response = await UserApi.login(loginParams);

    if (response.code === 200 && response.data) {
      // 保存token和用户信息
      uni.setStorageSync("token", response.data.token);
      uni.setStorageSync("userInfo", response.data.user);

      return response.data.user;
    } else {
      throw new Error(response.message || "登录失败");
    }
  } catch (error) {
    console.error("登录流程失败:", error);

    // 提供更详细的错误信息
    if (error instanceof Error) {
      if (error.message.includes('getUserProfile')) {
        throw new Error('获取用户信息失败，请确保在用户点击事件中调用登录方法');
      } else if (error.message.includes('login')) {
        throw new Error('获取微信登录凭证失败，请检查网络连接');
      } else if (error.message.includes('openid')) {
        throw new Error('获取用户身份信息失败，请重试');
      } else if (error.message.includes('HTTP错误') || error.message.includes('网络')) {
        throw new Error('网络连接失败，请检查网络设置后重试');
      }
    }

    throw error;
  }
};



// 获取本地存储的用户信息
export const getStoredUserInfo = (): UserInfo | null => {
  try {
    const userInfo = uni.getStorageSync("userInfo");
    return userInfo || null;
  } catch (error) {
    console.error("获取本地用户信息失败:", error);
    return null;
  }
};

// 获取本地存储的token
export const getStoredToken = (): string | null => {
  try {
    const token = uni.getStorageSync("token");
    return token || null;
  } catch (error) {
    console.error("获取本地token失败:", error);
    return null;
  }
};

// 清除用户信息
export const clearUserInfo = (): void => {
  try {
    uni.removeStorageSync("userInfo");
    uni.removeStorageSync("token");
  } catch (error) {
    console.error("清除用户信息失败:", error);
  }
};

// 检查是否已登录
export const isLoggedIn = (): boolean => {
  const token = getStoredToken();
  const userInfo = getStoredUserInfo();
  return !!(token && userInfo);
};

// 自动恢复登录状态
export const restoreLoginState = () => {
  const token = getStoredToken();
  const userInfo = getStoredUserInfo();

  if (token && userInfo) {
    console.log('恢复登录状态:', userInfo.nickname);
    return { token, userInfo };
  }

  return null;
};

// 验证token是否有效
export const validateToken = async (): Promise<boolean> => {
  try {
    const response = await UserApi.getUserInfo();
    return response.code === 200;
  } catch (error) {
    console.error('Token验证失败:', error);
    return false;
  }
};
