import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { UserInfo } from '@/utils/auth'

export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref<UserInfo | null>(null)
  const token = ref<string | null>(null)
  const openid = ref<string | null>(null)

  // 计算属性
  const isLoggedIn = computed(() => {
    return !!(token.value && userInfo.value)
  })

  const isAuthenticated = computed(() => {
    return !!openid.value
  })

  // 操作方法
  const setUserInfo = (info: UserInfo) => {
    userInfo.value = info
    // 同步到本地存储
    uni.setStorageSync('userInfo', info)
  }

  const setToken = (newToken: string) => {
    token.value = newToken
    // 同步到本地存储
    uni.setStorageSync('token', newToken)
  }

  const setOpenid = (newOpenid: string) => {
    openid.value = newOpenid
    // 同步到本地存储
    uni.setStorageSync('user_openid', newOpenid)
  }

  const clearUserData = () => {
    userInfo.value = null
    token.value = null
    openid.value = null
    
    // 清除本地存储
    uni.removeStorageSync('userInfo')
    uni.removeStorageSync('token')
    uni.removeStorageSync('user_openid')
  }

  const initFromStorage = () => {
    try {
      const storedUserInfo = uni.getStorageSync('userInfo')
      const storedToken = uni.getStorageSync('token')
      const storedOpenid = uni.getStorageSync('user_openid')
      
      if (storedUserInfo) {
        userInfo.value = storedUserInfo
      }
      if (storedToken) {
        token.value = storedToken
      }
      if (storedOpenid) {
        openid.value = storedOpenid
      }
    } catch (error) {
      console.error('从本地存储初始化用户数据失败:', error)
    }
  }

  return {
    // 状态
    userInfo,
    token,
    openid,
    
    // 计算属性
    isLoggedIn,
    isAuthenticated,
    
    // 方法
    setUserInfo,
    setToken,
    setOpenid,
    clearUserData,
    initFromStorage
  }
})
