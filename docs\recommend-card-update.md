# RecommendCard组件封面图片显示逻辑更新

## 更新概述

对 `src/components/RecommendCard.vue` 组件的封面图片显示逻辑进行了优化，使其与其他卡片组件（MeditationCard、ContentCard）保持一致的图片处理逻辑。

## 主要修改内容

### 1. Props接口更新
- 将 `cover` 属性改为可选属性 (`cover?: string`)
- 新增 `type` 属性 (`type?: 'meditation' | 'sleep' | 'sound'`)

### 2. 封面图片显示逻辑
添加了 `coverImage` 计算属性，实现智能封面图片选择：

```typescript
const coverImage = computed(() => {
  // 优先使用传入的封面图片
  if (props.cover) {
    return props.cover
  }
  
  // 如果没有封面图片，使用默认图片
  const type = props.type || 'meditation' // 默认为冥想类型
  const id = parseInt(props.id) || 1
  const typeImageIndex = ((id - 1) % 3) + 1
  const paddedIndex = String(typeImageIndex).padStart(2, '0')
  return `/static/images/${type}-card-cover-${paddedIndex}.png`
})
```

### 3. 模板更新
- 将 `:src="cover"` 改为 `:src="coverImage"`
- 使用计算属性动态生成封面图片路径

### 4. 使用方更新
更新了 `src/pages/plan/plan.vue` 中的RecommendCard使用：

```vue
<RecommendCard 
  v-for="course in recommendCourses" 
  :key="course.id"
  :id="course.id.toString()"
  :title="course.title"
  :description="course.description || ''"
  :cover="course.cover_url"
  :type="course.type"
  :duration="Math.round(course.duration / 60)"
  @click="onCourseClick" 
  @add-to-plan="onAddToPlan" 
/>
```

## 功能特性

### 1. 智能封面选择
- **优先级1**: 使用API返回的 `cover_url`
- **优先级2**: 根据 `type` 和 `id` 生成默认封面图片路径

### 2. 默认图片规则
- 根据内容类型（meditation/sleep/sound）选择对应的图片系列
- 每个类型有3张图片，根据ID循环使用
- 图片命名规则：`/static/images/{type}-card-cover-{01-03}.png`

### 3. 兼容性保证
- 保持向后兼容，仍支持直接传入cover属性
- 如果没有传入type，默认使用'meditation'类型
- 如果ID无效，默认使用1

## 与其他组件的一致性

现在RecommendCard组件与以下组件使用相同的封面图片逻辑：
- `MeditationCard.vue`
- `ContentCard.vue`
- `meditation-detail.vue`（详情页面）

## 优势

1. **统一性**: 所有卡片组件使用一致的图片处理逻辑
2. **可靠性**: 即使API不返回封面图片，也能显示美观的默认图片
3. **灵活性**: 支持API图片和默认图片的无缝切换
4. **维护性**: 图片路径生成逻辑集中管理，便于维护

## 测试建议

1. 测试有cover_url的情况，确保正确显示API图片
2. 测试无cover_url的情况，确保正确显示默认图片
3. 测试不同type的情况，确保选择正确的图片系列
4. 测试边界情况（无效ID、无type等）
