<template>
  <view class="navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
    <view class="navbar-content">
      <view class="navbar-left" @click="handleBack" v-if="showBack">
        <text class="back-icon">‹</text>
      </view>
      <view class="navbar-title">
        <text class="title-text">{{ title }}</text>
      </view>
      <view class="navbar-right">
        <slot name="right"></slot>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

interface Props {
  title?: string
  showBack?: boolean
  backgroundColor?: string
  textColor?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  showBack: true,
  backgroundColor: '#fff',
  textColor: '#000'
})

const statusBarHeight = ref(0)

const handleBack = () => {
  const pages = getCurrentPages()
  if (pages.length > 1) {
    uni.navigateBack()
  } else {
    uni.switchTab({
      url: '/pages/plan/plan'
    })
  }
}

onMounted(() => {
  // 获取状态栏高度
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
})
</script>

<style scoped>
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: v-bind(backgroundColor);
  z-index: 999;
  border-bottom: 2rpx solid #F7FAFC;
  backdrop-filter: blur(20rpx);
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  position: relative;
  padding: 0 32rpx;
}

.navbar-left {
  position: absolute;
  left: 32rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
  transition: all 0.15s ease;
}

.navbar-left:active {
  background-color: #F1F5F9;
}

.back-icon {
  font-size: 40rpx;
  color: v-bind(textColor);
  font-weight: 700;
}

.navbar-title {
  flex: 1;
  text-align: center;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: v-bind(textColor);
}

.navbar-right {
  position: absolute;
  right: 32rpx;
  display: flex;
  align-items: center;
}
</style>