<template>
  <view class="ad-carousel">
    <swiper 
      class="swiper" 
      :indicator-dots="true" 
      :autoplay="true" 
      :interval="3000"
      :duration="500"
      indicator-color="rgba(255, 255, 255, 0.3)"
      indicator-active-color="#ffffff"
    >
      <swiper-item v-for="(item, index) in ads" :key="index">
        <view class="ad-item" @click="onAdClick(item)">
          <image class="ad-image" :src="item.image" mode="aspectFill" />
          <view class="ad-content">
            <text class="ad-title">{{ item.title }}</text>
            <text class="ad-desc">{{ item.description }}</text>
          </view>
        </view>
      </swiper-item>
    </swiper>
  </view>
</template>

<script setup lang="ts">
interface AdItem {
  id: string
  title: string
  description: string
  image: string
  link?: string
}

interface Props {
  ads?: AdItem[]
}

interface Emits {
  (e: 'click', item: AdItem): void
}

const props = withDefaults(defineProps<Props>(), {
  ads: () => [
    {
      id: '1',
      title: '7天冥想挑战',
      description: '开启你的冥想之旅',
      image: 'https://picsum.photos/400/200?random=1'
    },
    {
      id: '2', 
      title: '深度睡眠冥想',
      description: '让你拥有更好的睡眠质量',
      image: 'https://picsum.photos/400/200?random=2'
    },
    {
      id: '3',
      title: '专注力提升',
      description: '通过冥想提高工作效率',
      image: 'https://picsum.photos/400/200?random=3'
    }
  ]
})

const emit = defineEmits<Emits>()

const onAdClick = (item: AdItem) => {
  emit('click', item)
}
</script>

<style scoped>
.ad-carousel {
  margin: 32rpx 24rpx;
  position: relative;
  z-index: 1;
}

.swiper {
  height: 360rpx;
  border-radius: 32rpx;
  overflow: hidden;
  box-shadow: 0 12rpx 40rpx rgba(127, 176, 105, 0.12), 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  border: 2rpx solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);
}

.ad-item {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.ad-item:active {
  transform: scale(0.98);
}

.ad-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.6s ease;
}

.ad-item:hover .ad-image {
  transform: scale(1.05);
}

.ad-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent 0%, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.7) 100%);
  backdrop-filter: blur(10rpx);
  padding: 60rpx 36rpx 36rpx;
  color: #ffffff;
}

.ad-title {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5rpx;
  line-height: 1.3;
}

.ad-desc {
  font-size: 26rpx;
  opacity: 0.95;
  font-weight: 500;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2);
  letter-spacing: 0.3rpx;
  line-height: 1.4;
}
</style>