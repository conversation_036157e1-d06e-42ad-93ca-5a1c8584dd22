<script setup lang="ts">
import { onLaunch, onShow, onHide } from "@dcloudio/uni-app";
import { useUserStore } from '@/stores/user'
import { useAuth } from '@/composables/useAuth'

onLaunch(() => {
  console.log("App Launch");

  // 初始化用户状态
  const userStore = useUserStore()
  const auth = useAuth()

  // 从本地存储初始化
  userStore.initFromStorage()

  // 恢复登录状态
  const restored = auth.initUserState()
  if (restored) {
    console.log("用户登录状态已恢复");
  } else {
    console.log("用户未登录");
  }
});

onShow(() => {
  console.log("App Show");
});

onHide(() => {
  console.log("App Hide");
});
</script>
<style></style>
