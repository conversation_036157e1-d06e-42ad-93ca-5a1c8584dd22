# 微信小程序登录使用说明

## 快速开始

### 1. 启动后端服务
确保你的Koa2后端服务运行在 `127.0.0.1:3000`，并且登录接口 `/api/public/user/login` 可用。

### 2. 测试登录功能
在微信开发者工具中访问测试页面：
```
/pages/test-login/test-login
```

### 3. 在其他页面使用登录
```vue
<template>
  <button @click="handleLogin">微信登录</button>
</template>

<script setup>
import { useAuth } from '@/composables/useAuth'

const auth = useAuth()

// 重要：登录方法必须在用户点击事件中同步调用
const handleLogin = () => {
  auth.login().then((userInfo) => {
    console.log('登录成功:', userInfo)
    // 处理登录成功逻辑
  }).catch((error) => {
    console.error('登录失败:', error)
    // 处理登录失败逻辑
  })
}
</script>
```

## 重要注意事项

### 微信API限制
- `wx.getUserProfile()` 只能在用户点击事件中直接调用
- 不能使用 `async/await` 包装登录方法
- 必须在按钮点击等用户交互事件中同步调用

### 正确的调用方式
```javascript
// ✅ 正确 - 在点击事件中同步调用
const handleLogin = () => {
  auth.login().then(userInfo => {
    // 处理成功
  }).catch(error => {
    // 处理错误
  })
}

// ❌ 错误 - 使用async/await会导致异步调用
const handleLogin = async () => {
  try {
    const userInfo = await auth.login() // 这会失败！
  } catch (error) {
    // 处理错误
  }
}
```

## 登录流程说明

1. 用户点击登录按钮
2. 同步调用 `wx.getUserProfile()` 获取用户信息
3. 异步调用 `uni.login()` 获取微信code
4. 构建登录参数并调用后端API
5. 保存token和用户信息
6. 更新应用状态

## 用户状态管理

使用 `useAuth()` 组合式函数管理用户状态：

```javascript
import { useAuth } from '@/composables/useAuth'

const auth = useAuth()

// 检查登录状态
console.log(auth.isLoggedIn)

// 获取用户信息
console.log(auth.userInfo)

// 获取openid
console.log(auth.openid)

// 退出登录
auth.logout()
```

## 故障排除

### 常见错误

1. **"getUserProfile:fail can only be invoked by user TAP gesture"**
   - 原因：在异步函数中调用了getUserProfile
   - 解决：确保在用户点击事件中同步调用登录方法

2. **网络请求失败**
   - 检查后端服务是否运行
   - 检查API地址配置是否正确
   - 在微信开发者工具中关闭域名校验（开发阶段）

3. **token过期**
   - 系统会自动清除过期的用户数据
   - 用户需要重新登录

## 开发环境配置

1. 在微信开发者工具中：
   - 设置 → 项目设置 → 本地设置
   - 勾选"不校验合法域名、web-view（业务域名）、TLS 版本以及 HTTPS 证书"

2. 确保后端CORS配置正确，允许小程序域名访问

## 生产环境部署

1. 修改 `src/utils/request.ts` 中的 `baseURL` 为生产环境地址
2. 在微信小程序后台配置合法域名
3. 确保后端服务使用HTTPS
4. 实现真实的微信code换取openid流程
