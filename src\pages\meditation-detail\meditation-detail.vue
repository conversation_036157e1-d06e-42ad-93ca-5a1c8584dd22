<template>
  <view class="meditation-detail">
    <!-- 顶部导航 -->
    <view class="nav-bar">
      <view class="nav-back" @click="goBack">
        <text class="nav-back-icon">‹</text>
      </view>
      <text class="nav-title">冥想详情</text>
      <view class="nav-placeholder"></view>
    </view>

    <!-- 可滚动内容区域 -->
    <scroll-view class="scroll-content" scroll-y="true">
      <!-- 封面图片 -->
      <view class="cover-section">
        <image class="cover-image" :src="coverImage" mode="aspectFill" />
        <view class="cover-overlay">
          <text class="cover-title">{{ apiData.title }}</text>
          <text class="cover-subtitle">{{ contentTypeText }} · {{ formatDuration(apiData.duration) }} · {{ apiData.favorite_count }}人收藏</text>
        </view>
      </view>

      <!-- 详情内容 -->
      <view class="content-section">
        <!-- 基本信息卡片 -->
        <view class="info-card">
          <text class="info-title">{{ isCourse ? '课程介绍' : '内容介绍' }}</text>
          <text class="info-description">{{ apiData.description || '暂无介绍' }}</text>
        </view>

        <!-- 标签显示 -->
        <view v-if="tagsArray.length > 0" class="info-card">
          <text class="info-title">标签</text>
          <view class="tags-container">
            <view v-for="tag in tagsArray" :key="tag" class="tag-item">
              <text class="tag-text">{{ tag }}</text>
            </view>
          </view>
        </view>

        <!-- 冥想课程列表 -->
        <view v-if="isCourse && apiData.children && apiData.children.length > 0" class="info-card">
          <text class="info-title">课程内容</text>
          <view class="course-list">
            <view
              v-for="(child, index) in apiData.children"
              :key="child.id"
              class="course-item"
              @click="goToCourseDetail(child.id)"
            >
              <image class="course-cover" :src="coverImage" mode="aspectFill" />
              <view class="course-info">
                <text class="course-title">{{ child.title }}</text>
                <text class="course-duration">{{ formatDuration(child.duration) }}</text>
              </view>
              <view class="course-arrow">
                <text class="arrow-icon">›</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部按钮 -->
    <view class="bottom-actions">
      <view class="action-btn favorite-btn" @click="toggleFavorite">
        <image
          class="favorite-icon"
          :src="isFavorited ? '/static/icons/like.png' : '/static/icons/unlike.png'"
          mode="aspectFit"
        />
      </view>
      <button class="action-btn join-btn" @click="showJoinPlanModal">加入计划</button>
      <button class="action-btn start-btn" @click="startMeditation">{{ isCourse ? '开始学习' : '立刻开始' }}</button>
    </view>

    <!-- 加入计划弹窗 -->
    <view v-if="isJoinPlanModalVisible" class="modal-overlay" @click="hideJoinPlanModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">加入计划</text>
          <view class="modal-close" @click="hideJoinPlanModal">
            <text class="close-icon">×</text>
          </view>
        </view>
        
        <view class="modal-body">
          <view class="form-item">
            <text class="form-label">开始日期</text>
            <picker mode="date" :value="planForm.startDate" @change="onDateChange">
              <view class="picker-input">
                <text class="picker-text">{{ planForm.startDate || '请选择日期' }}</text>
                <text class="picker-arrow">›</text>
              </view>
            </picker>
          </view>

          <view class="form-item">
            <text class="form-label">安排时间</text>
            <picker mode="time" :value="planForm.time" @change="onTimeChange">
              <view class="picker-input">
                <text class="picker-text">{{ planForm.time || '请选择时间' }}</text>
                <text class="picker-arrow">›</text>
              </view>
            </picker>
          </view>

          <view class="form-item">
            <text class="form-label">类型</text>
            <view class="radio-group">
              <view 
                class="radio-item" 
                :class="{ active: planForm.type === 'repeat' }"
                @click="planForm.type = 'repeat'"
              >
                <view class="radio-circle">
                  <view v-if="planForm.type === 'repeat'" class="radio-dot"></view>
                </view>
                <text class="radio-text">循环</text>
              </view>
              <view 
                class="radio-item" 
                :class="{ active: planForm.type === 'once' }"
                @click="planForm.type = 'once'"
              >
                <view class="radio-circle">
                  <view v-if="planForm.type === 'once'" class="radio-dot"></view>
                </view>
                <text class="radio-text">一次</text>
              </view>
            </view>
          </view>
        </view>

        <view class="modal-footer">
          <button class="modal-btn cancel-btn" @click="hideJoinPlanModal">取消</button>
          <button class="modal-btn confirm-btn" @click="confirmJoinPlan">确认</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { MeditationApi, type MeditationContent } from '@/api/meditation'

// 子课程接口
interface ChildCourse {
  id: string
  title: string
  duration: number
  cover_url?: string
}

// API数据结构（与后端响应保持一致）
interface ApiMeditationData {
  id: string
  type: 'meditation' | 'sleep' | 'sound'
  sub_type?: 'course' | 'single'
  parent_id?: string | null
  title: string
  description?: string
  cover_url?: string
  duration: number
  tags_text?: string
  favorite_count: number
  created_at: string
  updated_at?: string | null
  parent?: any
  children?: ChildCourse[]
  is_favorited: boolean
}

// API响应数据
const apiData = ref<ApiMeditationData>({
  id: '1',
  type: 'meditation',
  sub_type: 'course',
  parent_id: null,
  title: '正念冥想入门系列',
  description: '适合初学者的正念冥想课程，帮助你建立冥想习惯',
  cover_url: 'https://example.com/covers/mindfulness_course.jpg',
  duration: 0,
  tags_text: '正念,冥想入门,专注',
  favorite_count: 156,
  created_at: '2025-08-11T08:37:08.000Z',
  updated_at: null,
  parent: null,
  children: [],
  is_favorited: false
})

// 加载状态
const loading = ref(false)

// 收藏状态
const isFavorited = ref(false)

// 加入计划弹窗状态
const isJoinPlanModalVisible = ref(false)

// 计划表单数据
const planForm = ref({
  startDate: '',
  time: '',
  type: 'repeat' // 'repeat' 或 'once'
})

// 判断是否为课程类型
const isCourse = computed(() => {
  return apiData.value.type === 'meditation' && apiData.value.sub_type === 'course'
})

// 获取内容类型文本
const contentTypeText = computed(() => {
  const { type, sub_type } = apiData.value

  if (type === 'meditation') {
    return sub_type === 'course' ? '冥想课程' : '冥想'
  } else if (type === 'sleep') {
    return '助眠'
  } else if (type === 'sound') {
    return '声音'
  }
  return '冥想'
})

// 解析标签文本为数组
const tagsArray = computed(() => {
  if (!apiData.value.tags_text) return []
  return apiData.value.tags_text.split(',').filter(tag => tag.trim())
})

// 计算封面图片路径
const coverImage = computed(() => {
  // 优先使用API返回的封面图片
  // if (apiData.value.cover_url) {
  //   return apiData.value.cover_url
  // }

  // 如果没有封面图片，使用默认图片
  const type = apiData.value.type
  const id = parseInt(apiData.value.id) || 1
  const typeImageIndex = ((id - 1) % 3) + 1
  const paddedIndex = String(typeImageIndex).padStart(2, '0')
  return `/static/images/${type}-card-cover-01.png`
})

// 格式化时长显示
const formatDuration = (seconds: number): string => {
  if (seconds === 0) return '课程合集'
  const minutes = Math.floor(seconds / 60)
  return `${minutes}分钟`
}

onMounted(() => {
  // 根据路由参数获取具体的冥想详情
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage.options
  if (options && options.id) {
    loadMeditationDetail(options.id)
  }
})

// 根据id加载冥想详情
const loadMeditationDetail = async (id: string) => {
  try {
    loading.value = true
    const response = await MeditationApi.getDetail(parseInt(id))

    if (response.data) {
      // 直接使用API返回的数据结构
      apiData.value = {
        id: response.data.id.toString(),
        type: response.data.type,
        sub_type: response.data.sub_type,
        parent_id: response.data.parent_id?.toString() || null,
        title: response.data.title,
        description: response.data.description,
        cover_url: response.data.cover_url,
        duration: response.data.duration,
        tags_text: response.data.tags_text,
        favorite_count: response.data.favorite_count,
        created_at: response.data.created_at,
        updated_at: response.data.updated_at,
        parent: response.data.parent,
        children: response.data.children || [],
        is_favorited: response.data.is_favorited || false
      }
      isFavorited.value = response.data.is_favorited || false
    }
  } catch (error) {
    console.error('加载冥想详情失败:', error)
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none'
    })

    // 使用默认数据作为fallback
    apiData.value = {
      id: id,
      type: 'meditation',
      sub_type: 'single',
      parent_id: null,
      title: '正念冥想课程',
      description: '这是一个精心设计的冥想课程，帮助你在忙碌的生活中找到内心的平静与专注。',
      cover_url: '',
      duration: 900,
      tags_text: '正念,冥想,专注',
      favorite_count: 888,
      created_at: new Date().toISOString(),
      updated_at: null,
      parent: null,
      children: [],
      is_favorited: false
    }
  } finally {
    loading.value = false
  }
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 显示加入计划弹窗
const showJoinPlanModal = () => {
  isJoinPlanModalVisible.value = true
  // 设置默认日期为今天
  const today = new Date()
  const year = today.getFullYear()
  const month = String(today.getMonth() + 1).padStart(2, '0')
  const day = String(today.getDate()).padStart(2, '0')
  planForm.value.startDate = `${year}-${month}-${day}`
  
  // 设置默认时间为当前时间
  const hours = String(today.getHours()).padStart(2, '0')
  const minutes = String(today.getMinutes()).padStart(2, '0')
  planForm.value.time = `${hours}:${minutes}`
}

// 隐藏加入计划弹窗
const hideJoinPlanModal = () => {
  isJoinPlanModalVisible.value = false
}

// 日期选择变化
const onDateChange = (e: any) => {
  planForm.value.startDate = e.detail.value
}

// 时间选择变化
const onTimeChange = (e: any) => {
  planForm.value.time = e.detail.value
}

// 确认加入计划
const confirmJoinPlan = () => {
  if (!planForm.value.startDate || !planForm.value.time) {
    uni.showToast({
      title: '请完善计划信息',
      icon: 'none'
    })
    return
  }

  // 这里可以调用API保存计划
  console.log('计划信息:', planForm.value)
  
  uni.showToast({
    title: '已加入计划',
    icon: 'success'
  })
  
  hideJoinPlanModal()
}

// 跳转到子课程详情
const goToCourseDetail = (courseId: string) => {
  uni.navigateTo({
    url: `/pages/meditation-detail/meditation-detail?id=${courseId}`
  })
}

// 开始冥想
const startMeditation = () => {
  if (isCourse.value && apiData.value.children && apiData.value.children.length > 0) {
    // 如果是课程，跳转到第一个子课程
    const firstCourse = apiData.value.children[0]
    uni.navigateTo({
      url: `/pages/meditation/meditation?id=${firstCourse.id}`
    })
  } else {
    // 如果是单个冥想，直接开始
    uni.navigateTo({
      url: `/pages/meditation/meditation?id=${apiData.value.id}`
    })
  }
}

// 切换收藏状态
const toggleFavorite = async () => {
  try {
    const response = await MeditationApi.toggleFavorite(parseInt(apiData.value.id))

    if (response.data) {
      isFavorited.value = response.data.is_favorited
      apiData.value.is_favorited = response.data.is_favorited
      uni.showToast({
        title: isFavorited.value ? '已添加到收藏' : '已取消收藏',
        icon: 'success'
      })
    }
  } catch (error) {
    console.error('收藏操作失败:', error)
    uni.showToast({
      title: '操作失败，请重试',
      icon: 'none'
    })
  }
}
</script>

<style scoped>
.meditation-detail {
  height: 100vh;
  background: linear-gradient(180deg, #F0F9F0 0%, #F7FAFC 40%, #FFFFFF 100%);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}

.meditation-detail::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 400rpx;
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.08) 0%, rgba(168, 208, 141, 0.05) 50%, rgba(240, 249, 240, 0.03) 100%);
  pointer-events: none;
  z-index: 0;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 88rpx 24rpx 24rpx; /* 顶部增加状态栏高度 */
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(247, 250, 252, 0.9) 100%);
  backdrop-filter: blur(20rpx);
  flex-shrink: 0;
  z-index: 100;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.8);
  position: relative;
}

.nav-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.02) 0%, rgba(168, 208, 141, 0.01) 100%);
  pointer-events: none;
  z-index: 0;
}

.nav-back {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(247, 250, 252, 0.6) 100%);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(127, 176, 105, 0.08), 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  z-index: 1;
}

.nav-back:hover {
  transform: translateY(-2rpx) scale(1.05);
  box-shadow: 0 6rpx 20rpx rgba(127, 176, 105, 0.12), 0 3rpx 10rpx rgba(0, 0, 0, 0.06);
}

.nav-back:active {
  transform: translateY(0) scale(0.95);
}

.nav-back-icon {
  font-size: 52rpx;
  color: #2d3748;
  font-weight: bold;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}

.nav-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #2d3748;
  letter-spacing: 0.5rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.02);
  position: relative;
  z-index: 1;
}

.nav-placeholder {
  width: 72rpx;
}

/* 滚动内容区域 */
.scroll-content {
  flex: 1;
  background: transparent;
  position: relative;
  z-index: 1;
}

/* 封面区域 */
.cover-section {
  position: relative;
  height: 440rpx;
  overflow: hidden;
  border-radius: 0 0 32rpx 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.6s ease;
}

.cover-section:hover .cover-image {
  transform: scale(1.05);
}

.cover-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent 0%, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.7) 100%);
  backdrop-filter: blur(10rpx);
  padding: 80rpx 36rpx 40rpx;
}

.cover-title {
  display: block;
  font-size: 42rpx;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
  letter-spacing: 0.5rpx;
  line-height: 1.3;
}

.cover-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.95);
  font-weight: 500;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2);
  letter-spacing: 0.3rpx;
  line-height: 1.4;
}

/* 内容区域 */
.content-section {
  padding: 40rpx 24rpx 140rpx; /* 底部增加按钮高度的padding */
}

.info-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(247, 250, 252, 0.95) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 40rpx 36rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(127, 176, 105, 0.08), 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.02) 0%, rgba(168, 208, 141, 0.01) 100%);
  pointer-events: none;
  z-index: 0;
}

.info-card:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 12rpx 40rpx rgba(127, 176, 105, 0.12), 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.info-title {
  display: block;
  font-size: 32rpx;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 20rpx;
  letter-spacing: 0.5rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.02);
  position: relative;
  z-index: 1;
}

.info-description {
  font-size: 28rpx;
  color: #4a5568;
  line-height: 1.7;
  font-weight: 500;
  letter-spacing: 0.3rpx;
  position: relative;
  z-index: 1;
  opacity: 0.9;
}

/* 底部按钮 */
.bottom-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 32rpx 24rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(247, 250, 252, 0.9) 100%);
  backdrop-filter: blur(20rpx);
  border-top: 1rpx solid rgba(255, 255, 255, 0.8);
  gap: 20rpx;
  flex-shrink: 0;
  box-shadow: 0 -8rpx 32rpx rgba(127, 176, 105, 0.06);
}

.action-btn {
  height: 96rpx;
  border-radius: 48rpx;
  font-size: 30rpx;
  font-weight: 600;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.favorite-btn {
  width: 96rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(247, 250, 252, 0.8) 100%);
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 107, 157, 0.3);
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 157, 0.15);
}

.favorite-btn:hover {
  transform: translateY(-4rpx) scale(1.05);
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 157, 0.25);
  border-color: rgba(255, 107, 157, 0.5);
}

.favorite-btn:active {
  transform: translateY(-2rpx) scale(0.95);
}

.favorite-icon {
  width: 52rpx;
  height: 52rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(255, 107, 157, 0.2));
}

.join-btn {
  flex: 1;
  background: linear-gradient(135deg, rgba(226, 232, 240, 0.9) 0%, rgba(241, 245, 249, 0.8) 100%);
  backdrop-filter: blur(10rpx);
  color: #4a5568;
  border: 1rpx solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  letter-spacing: 0.5rpx;
}

.join-btn:hover {
  transform: translateY(-4rpx) scale(1.02);
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.15) 0%, rgba(168, 208, 141, 0.1) 100%);
  color: #7fb069;
  box-shadow: 0 8rpx 24rpx rgba(127, 176, 105, 0.15);
}

.join-btn:active {
  transform: translateY(-2rpx) scale(0.98);
}

.start-btn {
  flex: 1;
  background: linear-gradient(135deg, #7fb069 0%, #A8D08D 100%);
  color: #ffffff;
  box-shadow: 0 6rpx 20rpx rgba(127, 176, 105, 0.3), 0 2rpx 8rpx rgba(127, 176, 105, 0.2);
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
  letter-spacing: 0.5rpx;
}

.start-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.start-btn:hover {
  transform: translateY(-4rpx) scale(1.02);
  box-shadow: 0 10rpx 32rpx rgba(127, 176, 105, 0.4), 0 4rpx 12rpx rgba(127, 176, 105, 0.3);
}

.start-btn:hover::before {
  left: 100%;
}

.start-btn:active {
  transform: translateY(-2rpx) scale(0.98);
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(45, 55, 72, 0.6) 100%);
  backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;
  animation: modalFadeIn 0.3s ease-out;
}

.modal-content {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(247, 250, 252, 0.95) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  width: 100%;
  max-width: 640rpx;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(127, 176, 105, 0.15), 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  animation: modalSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.modal-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.02) 0%, rgba(168, 208, 141, 0.01) 100%);
  pointer-events: none;
  z-index: 0;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 36rpx 28rpx;
  border-bottom: 1rpx solid rgba(226, 232, 240, 0.6);
  position: relative;
  z-index: 1;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #2d3748;
  letter-spacing: 0.5rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.02);
}

.modal-close {
  width: 56rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 28rpx;
  background: linear-gradient(135deg, rgba(247, 250, 252, 0.8) 0%, rgba(226, 232, 240, 0.6) 100%);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: 1rpx solid rgba(255, 255, 255, 0.5);
}

.modal-close:hover {
  transform: scale(1.1);
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(248, 113, 113, 0.05) 100%);
  border-color: rgba(239, 68, 68, 0.2);
}

.modal-close:active {
  transform: scale(0.95);
}

.close-icon {
  font-size: 40rpx;
  color: #718096;
  line-height: 1;
  font-weight: bold;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}

.modal-close:hover .close-icon {
  color: #ef4444;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(40rpx) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-body {
  padding: 36rpx;
  max-height: 60vh;
  overflow-y: auto;
  position: relative;
  z-index: 1;
}

.form-item {
  margin-bottom: 40rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 20rpx;
  letter-spacing: 0.3rpx;
}

.picker-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 28rpx 24rpx;
  background: linear-gradient(135deg, rgba(247, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.6) 100%);
  backdrop-filter: blur(10rpx);
  border-radius: 16rpx;
  border: 1rpx solid rgba(226, 232, 240, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.picker-input:hover {
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.08) 0%, rgba(168, 208, 141, 0.05) 100%);
  border-color: rgba(127, 176, 105, 0.3);
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(127, 176, 105, 0.1);
}

.picker-text {
  font-size: 28rpx;
  color: #2d3748;
  font-weight: 500;
  letter-spacing: 0.3rpx;
}

.picker-arrow {
  font-size: 28rpx;
  color: #7fb069;
  transform: rotate(90deg);
  transition: all 0.3s ease;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}

.picker-input:hover .picker-arrow {
  transform: rotate(90deg) scale(1.1);
  color: #A8D08D;
}

.radio-group {
  display: flex;
  gap: 20rpx;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx 28rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, rgba(247, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.6) 100%);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(226, 232, 240, 0.8);
  flex: 1;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.radio-item:hover {
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.08) 0%, rgba(168, 208, 141, 0.05) 100%);
  border-color: rgba(127, 176, 105, 0.3);
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(127, 176, 105, 0.1);
}

.radio-item.active {
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.15) 0%, rgba(168, 208, 141, 0.1) 100%);
  border-color: #7fb069;
  box-shadow: 0 4rpx 16rpx rgba(127, 176, 105, 0.2);
}

.radio-circle {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 3rpx solid #cbd5e0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.8);
}

.radio-item.active .radio-circle {
  border-color: #7fb069;
  background: linear-gradient(135deg, #7fb069 0%, #A8D08D 100%);
  box-shadow: 0 2rpx 8rpx rgba(127, 176, 105, 0.3);
}

.radio-dot {
  width: 18rpx;
  height: 18rpx;
  border-radius: 50%;
  background: #ffffff;
  opacity: 0;
  transition: all 0.3s ease;
  box-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.radio-item.active .radio-dot {
  opacity: 1;
}

.radio-text {
  font-size: 28rpx;
  color: #2d3748;
  font-weight: 500;
  letter-spacing: 0.3rpx;
}

.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 28rpx 36rpx 36rpx;
  border-top: 1rpx solid rgba(226, 232, 240, 0.6);
  position: relative;
  z-index: 1;
}

.modal-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 30rpx;
  font-weight: 600;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  letter-spacing: 0.5rpx;
  position: relative;
  overflow: hidden;
}

.cancel-btn {
  background: linear-gradient(135deg, rgba(247, 250, 252, 0.9) 0%, rgba(241, 245, 249, 0.8) 100%);
  backdrop-filter: blur(10rpx);
  color: #4a5568;
  border: 1rpx solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
}

.cancel-btn:hover {
  transform: translateY(-4rpx) scale(1.02);
  background: linear-gradient(135deg, rgba(226, 232, 240, 0.15) 0%, rgba(241, 245, 249, 0.1) 100%);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
}

.cancel-btn:active {
  transform: translateY(-2rpx) scale(0.98);
}

.confirm-btn {
  background: linear-gradient(135deg, #7fb069 0%, #A8D08D 100%);
  color: #ffffff;
  box-shadow: 0 6rpx 20rpx rgba(127, 176, 105, 0.3), 0 2rpx 8rpx rgba(127, 176, 105, 0.2);
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.confirm-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.confirm-btn:hover {
  transform: translateY(-4rpx) scale(1.02);
  box-shadow: 0 10rpx 32rpx rgba(127, 176, 105, 0.4), 0 4rpx 12rpx rgba(127, 176, 105, 0.3);
}

.confirm-btn:hover::before {
  left: 100%;
}

.confirm-btn:active {
  transform: translateY(-2rpx) scale(0.98);
}

/* 标签样式 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-top: 12rpx;
  position: relative;
  z-index: 1;
}

.tag-item {
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.12) 0%, rgba(168, 208, 141, 0.08) 100%);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(127, 176, 105, 0.3);
  border-radius: 24rpx;
  padding: 12rpx 20rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.tag-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.05) 0%, rgba(168, 208, 141, 0.03) 100%);
  pointer-events: none;
  z-index: 0;
}

.tag-item:hover {
  transform: translateY(-2rpx) scale(1.02);
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.18) 0%, rgba(168, 208, 141, 0.12) 100%);
  border-color: rgba(127, 176, 105, 0.4);
  box-shadow: 0 4rpx 12rpx rgba(127, 176, 105, 0.15);
}

.tag-item:active {
  transform: translateY(0) scale(0.98);
}

.tag-text {
  height: 100%;
  font-size: 26rpx;
  color: #7fb069;
  font-weight: 600;
  letter-spacing: 0.3rpx;
  position: relative;
  z-index: 1;
}

/* 课程列表样式 */
.course-list {
  margin-top: 20rpx;
  position: relative;
  z-index: 1;
}

.course-item {
  display: flex;
  align-items: center;
  padding: 28rpx 20rpx;
  border-radius: 20rpx;
  margin-bottom: 16rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.6) 0%, rgba(247, 250, 252, 0.4) 100%);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.5);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.course-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.02) 0%, rgba(168, 208, 141, 0.01) 100%);
  pointer-events: none;
  z-index: 0;
}

.course-item:last-child {
  margin-bottom: 0;
}

.course-item:hover {
  transform: translateY(-2rpx) translateX(4rpx);
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.08) 0%, rgba(168, 208, 141, 0.05) 100%);
  box-shadow: 0 6rpx 20rpx rgba(127, 176, 105, 0.12), 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.course-item:active {
  transform: translateY(0) translateX(2rpx) scale(0.98);
}

.course-cover {
  width: 88rpx;
  height: 88rpx;
  border-radius: 18rpx;
  margin-right: 28rpx;
  flex-shrink: 0;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.course-item:hover .course-cover {
  transform: scale(1.05);
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.15);
}

.course-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  position: relative;
  z-index: 1;
}

.course-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #2d3748;
  line-height: 1.4;
  letter-spacing: 0.3rpx;
}

.course-duration {
  font-size: 26rpx;
  color: #718096;
  font-weight: 500;
  opacity: 0.9;
}

.course-arrow {
  width: 44rpx;
  height: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.1) 0%, rgba(168, 208, 141, 0.05) 100%);
  border-radius: 50%;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.course-item:hover .course-arrow {
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.15) 0%, rgba(168, 208, 141, 0.08) 100%);
  transform: translateX(4rpx) scale(1.1);
}

.arrow-icon {
  font-size: 32rpx;
  color: #7fb069;
  font-weight: bold;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}
</style>