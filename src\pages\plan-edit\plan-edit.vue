<template>
  <view class="plan-edit-page">
    <NavBar :title="isEdit ? '编辑计划' : '添加计划'" :show-back="true" />
    <view class="content">
      <view class="form-item">
        <text class="form-label">课程名称</text>
        <input class="form-input" v-model="planForm.courseName" placeholder="请输入课程名称" />
      </view>
      <view class="form-item">
        <text class="form-label">课时名称</text>
        <input class="form-input" v-model="planForm.lessonName" placeholder="请输入课时名称" />
      </view>
      <view class="save-button" @click="onSave">
        <text class="button-text">保存</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import NavBar from '@/components/NavBar.vue'

const isEdit = ref(false)
const planForm = reactive({
  id: '',
  courseName: '',
  lessonName: ''
})

onLoad((options) => {
  if (options.id) {
    isEdit.value = true
    // TODO: 根据id获取计划详情
    planForm.id = options.id
    planForm.courseName = options.courseName
    planForm.lessonName = options.lessonName
  }
})

const onSave = () => {
  console.log('保存计划', planForm)
  // TODO: 调用接口保存计划
  uni.showToast({ title: '保存成功', icon: 'success' })
  uni.navigateBack()
}
</script>

<style scoped>
.plan-edit-page {
  padding-top: 176rpx;
  min-height: 100vh;
  background-color: #F7FAFC;
}

.content {
  padding: 32rpx;
}

.form-item {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.form-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #2D3748;
  margin-bottom: 16rpx;
}

.form-input {
  font-size: 28rpx;
  color: #4A5568;
}

.save-button {
  margin-top: 48rpx;
  height: 96rpx;
  background: #7FB069;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(127, 176, 105, 0.3);
}

.button-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #ffffff;
}
</style>
