<template>
  <view class="meditation-page">
    <view class="meditation-header">
      <view class="back-btn" @click="goBack">
        <text class="back-icon">‹</text>
      </view>
      <text class="meditation-title">{{ meditationTitle }}</text>
      <view class="placeholder"></view>
    </view>
    
    <view class="meditation-content">
      <text class="meditation-text">冥想页面</text>
      <text class="meditation-subtitle">{{ meditationTitle }}</text>
      <text class="meditation-id">ID: {{ meditationId }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

const meditationId = ref('')
const meditationTitle = ref('冥想练习')

onMounted(() => {
  // 获取路由参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage.options
  
  if (options && options.id) {
    meditationId.value = options.id
    // 根据id设置标题（这里可以从服务器获取）
    const titleMap: Record<string, string> = {
      '101': '呼吸觉察冥想',
      '201': '深度放松冥想',
      '301': '单点专注冥想'
    }
    meditationTitle.value = titleMap[options.id] || '冥想练习'
  }
})

const goBack = () => {
  uni.navigateBack()
}
</script>

<style scoped>
.meditation-page {
  min-height: 100vh;
  background: #f7fafc;
  display: flex;
  flex-direction: column;
}

.meditation-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background: #ffffff;
  border-bottom: 1rpx solid #e2e8f0;
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  font-size: 48rpx;
  color: #2d3748;
  font-weight: bold;
}

.meditation-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3748;
}

.placeholder {
  width: 60rpx;
}

.meditation-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
}

.meditation-text {
  font-size: 48rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 24rpx;
}

.meditation-subtitle {
  font-size: 28rpx;
  color: #7fb069;
  margin-bottom: 16rpx;
}

.meditation-id {
  font-size: 24rpx;
  color: #718096;
}
</style>